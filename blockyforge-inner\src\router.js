const Logger = require("@common/Logger");
const Responses = require("@common/Responses");
const Middlewares = require("@common/Middlewares");
const moduleConfig = require("@config/modules");
const whiteConfig = require("@config/whitelist");

function checkWhitelist(req) {
    const ip = req.connection.remoteAddress;
    if (whiteConfig.enabled && !whiteConfig.allowedIps.includes(ip)) {
        Logger.warn(`Sender IP: ${ip}`);
        Logger.warn(`Endpoint info: [${req.method}] ${req.url}`);
        Logger.warn(`Endpoint headers: ${JSON.stringify(req.headers, null, 4)}`);
        return false;
    }

    return true;
}

function init(app) {
    // app.all("*", (req, res, next) => {
    //     if (!checkWhitelist(req)) {
    //         return;
    //     }

    //     next();
    // });

    for (var i = 0; i < moduleConfig.length; i++) {
        const endpoints = require(`@${moduleConfig[i]}-service/controller`);
        for (var j = 0; j < endpoints.length; j++) {
            createEndpoint(app, endpoints[j]);
        }
    }

    app.setNotFoundHandler((request, reply) => {
        Logger.warn(`Endpoint may not be implemented: [${request.method}] ${request.url}`);
        const notFound = Responses.notFound();
        reply.code(notFound.status).send(notFound.content);
    });
}

function createEndpoint(app, endpoint) {
    app.route({
        method: ["GET", "POST"],
        url: endpoint.path,
        handler: async (request, reply) => {
            try {
                const startTime = Date.now();
                if (!endpoint.methods.includes(request.method)) {
                    Logger.warn(`Endpoint method (${request.method}) may not be implemented: ${request.url}`);
                    const methodNotAllowed = Responses.methodNotAllowed();
                    return reply.code(methodNotAllowed.status).send(methodNotAllowed.content);
                }

                const { hasSucceeded, response: authResponse } = Middlewares.authenticateInner(request);
                if (!hasSucceeded) {
                    console.log(hasSucceeded);
                    // reply.code(authResponse.status).send(authResponse.content);
                    // return;
                }

                const response = await endpoint.functions[endpoint.methods.indexOf(request.method)](request);
                reply.code(response.status).send(response.content);

                const stopTime = Date.now();
                console.log(`${request.method} [${request.url}] Sent ${response.status} | Delta: ${stopTime - startTime}ms`);
                if (response.status == 500) {
                    console.log("\x1b[91m%s\x1b[0m", `Response: ${JSON.stringify(response.content)}`);
                }
            } catch (e) {
                Logger.error(`Error occurred with API: ${req.url}`);
                Logger.error(e);

                const innerError = Responses.innerError();
                reply.code(innerError.status).send(innerError.content);
            }
        }
    });
}
module.exports = init;
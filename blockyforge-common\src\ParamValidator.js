const langConfig = require("@common-config/full_languages");
const paramsConfig = require("@common-config/params");
const Currencies = require("@common-constants/Currencies");
const Genders = require("@common-constants/Genders");

module.exports = class ParamValidator {
    static async checkNickname(nickName) {
        if (!nickName) {
            return null;
        }
    
        if (nickName.length < paramsConfig.nicknameMinLength || nickName.length > paramsConfig.nicknameMaxLength) {
            return null;
        }
        
        const regexNum = new RegExp(/^\d/); // checks if nickname starts with a digit
        if (regexNum.test(nickName)) {
            return null;
        }
    
        const regex = new RegExp(paramsConfig.nicknameRegex);
        if (!regex.test(nickName)) {
            return null;
        }
    
        return nickName;
    }
    
    static checkSex(sex) {
        sex = parseInt(sex);
        if (!sex) {
            return null;
        }
    
        if (sex != Genders.BOY && sex != Genders.GIRL) {
            return null;
        }
    
        return sex;
    }
    
    static checkParamLength(param, maxLength, minLength) {
        if (!param) {
            return null;
        }
    
        if (param.length > maxLength) {
            return null;
        }
    
        if (minLength && param.length < minLength) {
            return null;
        }
    
        return param;
    }
    
    static checkPageParams(pageNo, pageSize) {
        pageNo = parseInt(pageNo);
        pageSize = parseInt(pageSize);
        
        if (isNaN(pageNo)) {
            pageNo = 0;
        }
    
        if (isNaN(pageSize)) {
            pageSize = 10;
        } else if (pageSize > 50) {
            pageSize = 50;
        }
    
        return { pageNo, pageSize }
    }
    
    static checkLanguage(language) {
        if (!language) {
            return null;
        }
    
        language = language.substring(0, 2).toLowerCase();
        if (!langConfig.includes(language)) {
            return null;
        }
    
        return language;
    }
    
    static checkCurrency(currency, includeClanGold) {
        currency = parseInt(currency);
        if (!currency) {
            return null;
        }
    
        if (currency != Currencies.GOLD && currency != Currencies.DIAMOND && !includeClanGold) {
            return null;
        }
    
        if (includeClanGold && currency != Currencies.CLAN_GOLD) {
            return null;
        }
    
        return currency;
    }
    
    static checkPassword(password) {
        if (password.length < paramsConfig.passwordMinLength) {
            return null;
        }
    
        if (password.length > paramsConfig.passwordMaxLength) {
            return null;
        }
    
        const regex = new RegExp(paramsConfig.passwordRegex);
        if (!regex.test(password)) {
            return null;
        }
        
        return password;
    }
    
    static checkSha1(hash) {
        if (!hash) {
            return null;
        }
    
        // Size of a SHA-1 Hash in hexadecimal representation
        if (hash.length != 40) {
            return null;
        }
    
        const regex = new RegExp(paramsConfig.sha1Regex);
        if (!regex.test(hash)) {
            return null;
        }
    
        return hash;
    }

    static checkEmail(email) {
        if (!email) {
            return null;
        }

        const regex = new RegExp(paramsConfig.emailRegex);
        if (!regex.test(email)) {
            return null;
        }

        return email;
    }
}
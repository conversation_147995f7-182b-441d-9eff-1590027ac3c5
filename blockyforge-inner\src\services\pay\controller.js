const service = require("@pay-service/service");

async function getUserWealth(request) {
    const userId = parseInt(request.params.userId);
    return await service.getUserWealth(userId);
}

async function buyGameProp(request) {
    const userId = parseInt(request.body.userId);
    const currency = parseInt(request.body.currency);
    const quantity = parseInt(request.body.quantity);
    const gameId = request.body.gameId;
    const propId = request.body.propsId;
    
    return await service.buyGameProp(userId, currency, quantity, gameId, propId);
}

module.exports = [
    {
        "path": "/pay/i/api/v1/wealth/users/:userId",
        "methods": ["GET"],
        "functions": [getUserWealth]
    },
    {
        "path": "/pay/api/v1/pay/users/purchase/game/props",
        "methods": ["POST"],
        "functions": [buyGameProp]
    }
]
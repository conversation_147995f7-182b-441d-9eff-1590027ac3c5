const crypto = require("crypto");
const Logger = require("@common/Logger");
const authConfig = require("@common-config/auth");
const hostConfig = require("@common-config/host");
const cdnConfig = require("@common-config/cdn");

const cached = {};
const reloadKey = crypto.createHash("sha1").update(authConfig.configReloadKey).digest("hex");

module.exports = class ConfigManager {
    static async get(configName, reloadKey) {
        const cachedData = cached[configName];

        if (!reloadKey && cachedData) {
            return cachedData;
        }
    
        if (reloadKey && cachedData && !this.checkReloadKey(reloadKey)) {
            return cachedData;
        }

        try {
            Logger.debug(`Retrieving configuration ${configName} (Key: ${reloadKey ? reloadKey : "unspecified"})`);
            const res = await fetch(`${hostConfig.cdnUrl}/${cdnConfig.configPath}/${configName}.json`);            
            const data = await res.json();
            cached[configName] = data;
            return data;
        } catch (err) {
            Logger.warn(`Failed to retrieve configuration (${configName})`);
            Logger.warn(err);

            return cachedData || null;
        }
    }
    
    static checkReloadKey(userReloadKey) {
        if (userReloadKey.length != reloadKey.length) {
            return false;
        }
    
        return crypto.timingSafeEqual(Buffer.from(userReloadKey), Buffer.from(reloadKey));
    }
}
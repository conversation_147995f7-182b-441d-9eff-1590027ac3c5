const ConfigManager = require("@common/ConfigManager");
const Responses = require("@common/Responses");
const Logger = require("@common/Logger");

async function getBlockmodsConfig() {
    try {
        Logger.debug("Fetching blockmods-config from CDN");
        const configData = await ConfigManager.get("blockmods-config");

        if (!configData) {
            Logger.warn("Config file not found: blockmods-config");
            return Responses.notFound();
        }

        return Responses.success(configData);
    } catch (error) {
        Logger.error("Error fetching blockmods-config:", error);
        return Responses.innerError();
    }
}

async function getBlockymodsCheckVersion() {
    try {
        Logger.debug("Fetching blockymods-check-version from CDN");
        const configData = await ConfigManager.get("blockymods-check-version");

        if (!configData) {
            Logger.warn("Config file not found: blockymods-check-version");
            return Responses.notFound();
        }

        return Responses.success(configData);
    } catch (error) {
        Logger.error("Error fetching blockymods-check-version:", error);
        return Responses.innerError();
    }
}

async function getBlockymodsCheckRes() {
    try {
        Logger.debug("Fetching blockymods-check-res from CDN");
        const configData = await ConfigManager.get("blockymods-check-res");

        if (!configData) {
            Logger.warn("Config file not found: blockymods-check-res");
            return Responses.notFound();
        }

        return Responses.success(configData);
    } catch (error) {
        Logger.error("Error fetching blockymods-check-res:", error);
        return Responses.innerError();
    }
}

async function getIndiegameCheckVersion() {
    try {
        Logger.debug("Fetching indiegame-check-version from CDN");
        const configData = await ConfigManager.get("indiegame-check-version");

        if (!configData) {
            Logger.warn("Config file not found: indiegame-check-version");
            return Responses.notFound();
        }

        return Responses.success(configData);
    } catch (error) {
        Logger.error("Error fetching indiegame-check-version:", error);
        return Responses.innerError();
    }
}

async function getIndiegameConfig() {
    try {
        Logger.debug("Fetching indiegame-config from CDN");
        const configData = await ConfigManager.get("indiegame-config");

        if (!configData) {
            Logger.warn("Config file not found: indiegame-config");
            return Responses.notFound();
        }

        return Responses.success(configData);
    } catch (error) {
        Logger.error("Error fetching indiegame-config:", error);
        return Responses.innerError();
    }
}

module.exports = {
    getBlockmodsConfig,
    getBlockymodsCheckVersion,
    getBlockymodsCheckRes,
    getIndiegameCheckVersion,
    getIndiegameConfig
};

const Responses = require("@common/Responses");
const Logger = require("@common/Logger");
const hostConfig = require("@common-config/host");
const cdnConfig = require("@common-config/cdn");

async function getBlockmodsConfig() {
    try {
        Logger.debug("Fetching blockmods-config from CDN");
        const url = `${hostConfig.cdnUrl}/${cdnConfig.configPath}/blockmods-config.json`;
        Logger.debug(`CDN URL: ${url}`);

        const response = await fetch(url);

        if (!response.ok) {
            Logger.warn(`Failed to fetch blockmods-config: HTTP ${response.status}`);
            return Responses.notFound();
        }

        const configData = await response.json();
        Logger.debug("Successfully fetched blockmods-config");

        return Responses.success(configData);
    } catch (error) {
        Logger.error("Error fetching blockmods-config:");
        Logger.error(error);
        return Responses.innerError();
    }
}

module.exports = {
    getBlockmodsConfig
};

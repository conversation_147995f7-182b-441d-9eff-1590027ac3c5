const Responses = require("@common/Responses");
const vipPrivileges = require("@common-config/privileges");
const RequestStatuses = require("@common-constants/RequestStatuses");
const Friend = require("@common-models/Friend");
const FriendRequest = require("@common-models/FriendRequest");
const GameStatus = require("@common-models/GameStatus");
const Localization = require("@common-models/Localization");
const Page = require("@common-models/Page");
const User = require("@common-models/User");
const Vip = require("@common/models/Vip");

async function getFriendList(userId, pageNo, pageSize) {
    const friends = await Friend.listFromUserId(userId, pageNo, pageSize);
    return Responses.success(friends);
}

async function getFriendRequestList(userId, pageNo, pageSize) {
    const friendRequests = await FriendRequest.listFromUserId(userId, pageNo, pageSize);
    return Responses.success(friendRequests);
}

async function searchFriendByNickname(userId, nickName, pageNo, pageSize) {
    if (!nickName) {
        return Responses.success(Page.empty());
    }

    const excludeList = await Friend.listIdsFromUserId(userId);
    const results = await Friend.search(userId, nickName, excludeList, pageNo, pageSize);
    return Responses.success(results);
}

async function searchFriendById(userId, friendId) {
    if (userId == friendId) {
        return Responses.success();
    }

    const hasFriend = await Friend.isFriend(userId, friendId);
    if (hasFriend) {
        return Responses.success();
    }

    const isFriendExists = await User.exists(friendId);
    if (!isFriendExists) {
        return Responses.success();
    }

    return getFriendInfo(userId, friendId);
}

async function sendFriendRequest(userId, friendId, message) {
    if (userId == friendId) {
        return Responses.notValidUser();
    }

    const hasFriend = await Friend.isFriend(userId, friendId);
    if (hasFriend) {
        return Responses.alreadyFriend();
    }

    const isFriendExists = await User.exists(friendId);
    if (!isFriendExists) {
        return Responses.notValidUser();
    }

    const user = await User.fromUserId(userId);
    const userLocale = await Localization.fromUserId(userId);

    const request = await FriendRequest.findFirst(friendId, userId, RequestStatuses.PENDING);
    if (request.getRequestId() != null) {
        request.setMessage(message);
        request.setProfilePic(user.getProfilePic());
        request.setNickname(user.getNickname());
    } else {
        request.setUserId(friendId); // receiver
        request.setFriendId(userId); // sender
        request.setMessage(message);
        request.setProfilePic(user.getProfilePic());
        request.setNickname(user.getNickname());
        request.setSex(user.getSex());
        request.setCountry(userLocale.getCountry());
        request.setLanguage(userLocale.getLanguage());
        request.setCreationTime(Date.now());
    }

    await request.save();
    return Responses.success();
}

async function deleteFriend(userId, friendId) {
    const hasFriend = await Friend.isFriend(userId, friendId);
    if (!hasFriend) {
        return Responses.notValidUser();
    }

    await Friend.removeFriend(userId, friendId);    
    return Responses.success();
}

async function addFriendAlias(userId, friendId, alias) {
    const friend = await Friend.fromUserId(userId, friendId);
    if (!friend) {
        return Responses.notValidUser();
    }

    friend.setAlias(alias);
    await friend.save();

    return Responses.success();
}

async function deleteFriendAlias(userId, friendId) {
    const friend = await Friend.fromUserId(userId, friendId);
    if (!friend) {
        return Responses.notValidUser();
    }

    friend.setAlias("");
    await friend.save();

    return Responses.success();
}

async function acceptFriendRequest(userId, friendId) {
    const isFriendExists = await User.exists(friendId);
    if (userId == friendId || !isFriendExists) {
        return Responses.notValidUser();
    }

    const friendRequest = await FriendRequest.findFirst(userId, friendId, RequestStatuses.PENDING);
    if (!friendRequest.getRequestId()) {
        return Responses.notValidUser();
    }

    friendRequest.setStatus(RequestStatuses.ACCEPTED);
    await friendRequest.save();

    await Friend.addFriend(userId, friendId);
    return Responses.success();
}

async function rejectFriendRequest(userId, friendId) {
    const isFriendExists = await User.exists(friendId);
    if (userId == friendId || !isFriendExists) {
        return Responses.notValidUser();
    }

    const friendRequest = await FriendRequest.findFirst(userId, friendId, RequestStatuses.PENDING);
    if (!friendRequest.getRequestId()) {
        return Responses.notValidUser();
    }

    friendRequest.setStatus(RequestStatuses.REFUSED);
    await friendRequest.save();

    return Responses.success();
}

async function getFriendInfo(userId, friendId) {
    const isFriendExists = await User.exists(friendId);
    if (!isFriendExists) {
        return Responses.userNotExists();
    }

    const friendInfo = await Friend.getInfo(friendId);
    const friend = await Friend.fromUserId(userId, friendId);

    if (friend) {
        friendInfo.friend = true;
        friendInfo.alias = friend.getAlias();
    }

    return Responses.success(friendInfo);
}

async function getFriendStatus(userId) {
    const vip = await Vip.fromUserId(userId);

    const userFriendList = await Friend.listIdsFromUserId(userId);
    const data = {
        curFriendCount: userFriendList.length,
        maxFriendCount: vipPrivileges[vip.getLevel()].maxFriends,
        status: []
    };

    for (let i = 0; i < userFriendList.length; i++) {
        const friendGameStatus = new GameStatus(userFriendList[i]);
        data.status.push(friendGameStatus);
    }

    return Responses.success(data);
}

async function getFriendRecommendation(userId) {
    const recommendation = await Friend.getRecommendation(userId, 10);
    return Responses.success(recommendation);
}

module.exports = {
    getFriendList: getFriendList,
    getFriendRequestList: getFriendRequestList,
    searchFriendByNickname: searchFriendByNickname,
    searchFriendById: searchFriendById,
    sendFriendRequest: sendFriendRequest,
    deleteFriend: deleteFriend,
    addFriendAlias: addFriendAlias,
    deleteFriendAlias: deleteFriendAlias,
    acceptFriendRequest: acceptFriendRequest,
    rejectFriendRequest: rejectFriendRequest,
    getFriendInfo: getFriendInfo,
    getFriendStatus: getFriendStatus,
    getFriendRecommendation: getFriendRecommendation
}
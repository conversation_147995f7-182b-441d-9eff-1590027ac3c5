const Logger = require("@common/Logger");
const Responses = require("@common/Responses");
const ActivityTypes = require("@common-constants/ActivityTypes");
const ActivityManager = require("@activities/manager");

async function getSignInActivity(userId) {
    const activity = ActivityManager.getActivity("sign_in", ActivityTypes.SIGN_IN);
    if (!activity) {
        return Responses.activityNotExists();
    }

    const activityInfo = await activity.getHandler().getInfo(userId);
    return Responses.success(activityInfo);
}

async function receiveSignInReward(userId) {
    const activity = ActivityManager.getActivity("sign_in", ActivityTypes.SIGN_IN);
    if (!activity) {
        return Responses.activityNotExists();
    }

    const claimInfo = await activity.getHandler().claimReward(userId);
    switch (claimInfo.code) {
        case activity.getHandler().ERROR_ALREADY_CLAIMED:
            return Responses.activityFinished();
    }

    return Responses.success(claimInfo);
}

async function getActivityList(userId) {
    Logger.warn("GetActivityList: Implementation needed");
    return Responses.success({
        activityTitleList: [
            //{ "titleName": "Daily Bonus", "titleType": "weekend", "content": "Weekend Login Gift" },
            { "titleName": "Login Gift", "pic": "https://static.sandboxol.com/sandbox/activity/sign_up_gift/sign_gift.png", "content": "activity:sign" },
            { "titleName": "Prize Wheel", "pic": "https://ayinamyit.moonsveil.xyz/activity/banner/actwheel.png", "content": "activity:wheel" }
        ]
    });
}

async function getActivityTaskList(userId, activityType) {
    Logger.warn("GetActivityTaskList: Implementation needed");
    const actions = [
        {
            "actionId": 7,
            "actionName": "Login on Saturday",
            "quantity": 1,
            "dateType": "day",
            "condition": "6",
            "actionType": "common",
            "actionFlag": "saturday_login",
            "actionRewards": [
                {
                    "rewardName": "1",
                    "rewardType": "diamond",
                    "quantity": 1,
                    "rewardPic": "https://static.sandboxol.com/sandbox/activity/treasurebox/Bcubes.png"
                },
                {
                    "rewardName": "200",
                    "rewardType": "gold",
                    "quantity": 200,
                    "rewardPic": "https://static.sandboxol.com/sandbox/activity/treasurebox/coin.png"
                }
            ]
        },
        {
            "actionId": 8,
            "actionName": "Login on Sunday",
            "quantity": 1,
            "dateType": "day",
            "condition": "7",
            "actionType": "common",
            "actionFlag": "sunday_login",
            "status": 1,
            "actionRewards": [
                {
                    "rewardName": "1",
                    "rewardType": "diamond",
                    "quantity": 1,
                    "rewardPic": "https://static.sandboxol.com/sandbox/activity/treasurebox/Bcubes.png"
                },
                {
                    "rewardName": "200",
                    "rewardType": "gold",
                    "quantity": 200,
                    "rewardPic": "https://static.sandboxol.com/sandbox/activity/treasurebox/coin.png"
                }
            ]
        }
    ];

    return Responses.success(activityType ? actions : []);
}

async function getActivityFreeWheelStatus(userId, activityId) {
    const activity = ActivityManager.getActivity(activityId, ActivityTypes.WHEEL);
    if (!activity) {
        return Responses.activityNotExists();
    }

    const freeWheelStatus = await activity.getHandler().getFreeWheelStatus(userId);
    return Responses.success(freeWheelStatus);
}

async function getActivityWheelInfo(userId, activityId, type) {
    const activity = ActivityManager.getActivity(activityId, ActivityTypes.WHEEL);
    if (!activity) {
        return Responses.activityNotExists();
    }

    const activityInfo = await activity.getHandler().getInfo(userId, type) ?? {};
    return Responses.success(activityInfo);
}

async function playActivityWheel(userId, activityId, type, isMultiplePlay) {
    const activity = ActivityManager.getActivity(activityId, ActivityTypes.WHEEL);
    if (!activity) {
        return Responses.activityNotExists();
    }

    const playInfo = await activity.getHandler().playWheel(userId, type, isMultiplePlay);
    if (!playInfo) {
        return Responses.notEnoughWealth();
    }

    return Responses.success(playInfo);
}

async function getActivityWheelShopInfo(userId, activityId, type) {
    const activity = ActivityManager.getActivity(activityId, ActivityTypes.WHEEL);
    if (!activity) {
        return Responses.activityNotExists();
    }

    const shopInfo = await activity.getHandler().getShopInfo(userId, type);
    return Responses.success(shopInfo);
}

async function exchangeBlock(userId, activityId, type, rewardId) {
    const activity = ActivityManager.getActivity(activityId, ActivityTypes.WHEEL);
    if (!activity) {
        return Responses.activityNotExists();
    }

    const exchangeInfo = await activity.getHandler().exchangeBlock(userId, type, rewardId);
    switch (exchangeInfo.code) {
        case activity.getHandler().ERROR_REWARD_NOT_EXISTS:
            return Responses.invalidParameter();
        case activity.getHandler().ERROR_NOT_ENOUGH_BLOCKS:
            return Responses.notEnoughBlocks();
        case activity.getHandler().ERRROR_ALREADY_OWNED:
            return Responses.alreadyOwned();
    }

    return Responses.success(exchangeInfo);
}

module.exports = {
    getSignInActivity: getSignInActivity,
    receiveSignInReward: receiveSignInReward,
    getActivityList: getActivityList,
    getActivityTaskList: getActivityTaskList,
    getActivityFreeWheelStatus: getActivityFreeWheelStatus,
    getActivityWheelInfo: getActivityWheelInfo,
    getActivityWheelShopInfo: getActivityWheelShopInfo,
    playActivityWheel: playActivityWheel,
    exchangeBlock: exchangeBlock
}
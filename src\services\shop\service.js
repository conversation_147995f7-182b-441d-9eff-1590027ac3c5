const Dressing = require("@common/Dressing");
const Responses = require("@common/Responses");
const logger = require("@common/Logger");
const vipPrivileges = require("@common-config/privileges");
const Currencies = require("@common-constants/Currencies");
const DressOwnerTypes = require("@common-constants/DressOwnerTypes");
const TransactionMethods = require("@common-constants/TransactionMethods");
const DressOptions = require("@common-models/DressOptions");
const Page = require("@common-models/Page");
const User = require("@common-models/User");
const Vip = require("@common-models/Vip");
const payService = require("@pay-service/base");
const decorationService = require("@decoration-service/base");

async function getDressList(userId, categoryId, currencyType, pageNo, pageSize) {
    const user = await User.fromUserId(userId);
    const ownedDresses = await decorationService.getOwnedDresses(userId);
    
    const dressesData = Dressing.getDresses(new DressOptions({
        categoryId: categoryId, 
        currency: currencyType,
        sex: user.getSex(),
        showShopOnly: true, 
        ownedDresses: ownedDresses,
        ownerType: DressOwnerTypes.TAG_ITEM
    }));

    const data = new Page(dressesData, dressesData.length, pageNo, pageSize);
    return Responses.success(data);
}

async function buyDresses(userId, dressIds) {
    const user = await User.fromUserId(userId);
    const vip = await Vip.fromUserId(userId);

    let diamondsNeeded = 0;
    let goldNeeded = 0;

    const ownedDresses = await decorationService.getOwnedDresses(userId);

    const purchasedDresses = [];
    const purchaseResults = {};

    for (let i = 0; i < dressIds.length; i++) {
        if (ownedDresses.includes(dressIds[i])) {
            continue;
        }

        const dressInfo = Dressing.getDressInfo(dressIds[i]);
        if (!dressInfo) {
            purchaseResults[dressIds[i]] = false;
            continue;
        }

        if (!dressInfo.inShop) {
            continue;
        }

        if (dressInfo.sex != 0 && user.getSex() != dressInfo.sex) {
            continue;
        }

        let hasEnoughCurrency = await payService.hasEnoughCurrency(userId, dressInfo.currency, dressInfo.price);
        if (!hasEnoughCurrency) {
            switch (dressInfo.currency) {
                case Currencies.GOLD:
                    goldNeeded += dressInfo.price;
                    break;
                case Currencies.DIAMOND:
                    diamondsNeeded += dressInfo.price;
                    break;
            }

            purchaseResults[dressInfo.id] = false;
            continue;
        }

        let discount = vipPrivileges[vip.getLevel()].shopDiscount;
        let actualPrice = dressInfo.price * (100 - discount) / 100;

        const { hasFailed } = await payService.removeCurrency(userId, dressInfo.currency, actualPrice, TransactionMethods.SHOP_PURCHASE);
        purchaseResults[dressInfo.id] = !hasFailed;

        if (!hasFailed) {
            purchasedDresses.push(dressInfo.id);
        }
    }

    // TODO: Proceed to payment only if the user has enough of each currency to buy all items
    decorationService.addDresses(userId, purchasedDresses);
    
    if (diamondsNeeded > 0 || goldNeeded > 0) {
        return Responses.notEnoughWealth({
            decorationPurchaseStatus: purchaseResults,
            diamondsNeed: diamondsNeeded,
            goldsNeed: goldNeeded,
        });
    }

    return Responses.success({
        decorationPurchaseStatus: purchaseResults
    });
}

async function buyGameProp(userId, gameId, propId) {
    logger.warn("BuyGameProp: Implementation needed");
    return Responses.innerError();
}

async function buyGame(userId, gameId) {
    logger.warn("BuyGame: Implementation needed");
    return Responses.innerError();
}

module.exports = {
    getDressList: getDressList,
    buyDresses: buyDresses,
    buyGameProp: buyGameProp,
    buyGame: buyGame
}

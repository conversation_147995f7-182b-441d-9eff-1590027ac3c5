const Responses = require("@common/Responses");
const GameData = require("@models/GameData")

async function getUserGamedata(userId, tableName, subKey) {
    const gameData = await GameData.fromUserId(userId, tableName, subKey);
    console.log(gameData.response());
    return Responses.success(gameData.response());
}

async function postUserGamedata(tableName, dataPosts) {
    for (let i = 0; i < dataPosts.length; i++) {
        const post = dataPosts[i];

        const gameData = new GameData();
        gameData.setUserId(post.userId);
        gameData.setSubKey(post.subKey);
        gameData.setData(post.data);
        
        await gameData.save(tableName);
    }

    return Responses.success();
}

module.exports = {
    getUserGamedata: getUserGamedata,
    postUserGamedata: postUserGamedata
}
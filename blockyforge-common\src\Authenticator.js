const crypto = require("crypto");
const authConfig = require("@common-config/auth");
const policyConfig = require("@common-config/policies");

module.exports = class Authenticator {
    static createUserToken(payload) {
        let salt = crypto.randomBytes(32).toString("base64")
                                         .replace(/=+$/, "");
        
        payload = JSON.stringify(payload);
        payload = Buffer.from(payload).toString("base64")
                                      .replace(/=+$/, "");

        const base64Sig = crypto.createHmac("sha256", authConfig.tokenSecretKey)
                                .update(`${salt}.${payload}`)
                                .digest("base64");

        const dbToken = `${salt}.${payload}`;
        const userToken = `${salt}.${payload}.${base64Sig}`;
                                  
        return { dbToken: dbToken, userToken: userToken };
    }

    static verifyUserToken(userToken, dbToken) {
        if (!userToken || !dbToken) {
           return { isValid: false }
        }
     
        let [ salt, payload, signature] = userToken.split('.');
        if (!salt || !signature) {
           return { isValid: false };
        }
     
        const userComputedSignature = crypto.createHmac("sha256", authConfig.tokenSecretKey)
                                            .update(`${salt}.${payload}`)
                                            .digest("base64");
     
        if (signature.length != userComputedSignature.length) {
           return { isValid: false };
        }
     
        if (!crypto.timingSafeEqual(Buffer.from(signature), Buffer.from(userComputedSignature))) {
           return { isValid: false };
        }
     
        const dbComputedToken = crypto.createHmac("sha256", authConfig.tokenSecretKey)
                                      .update(dbToken)
                                      .digest("base64");
        
        if (signature.length != dbComputedToken.length) {
           return { isValid: false };
        }
     
        const isValid = crypto.timingSafeEqual(Buffer.from(signature), Buffer.from(dbComputedToken));
        const parsedPayload = JSON.parse(
           Buffer.from(payload, "base64").toString("ascii")
        );
     
        return { isValid: isValid, payload: parsedPayload };
    }

    static createServerToken() {
        const timestamp = Date.now();
    
        const nonceDigits = authConfig.innerSecretKey.match(/\d/g).join('');
        const nonceLength = nonceDigits.length;
        const nonce = (
            timestamp + Math.abs(parseInt(nonceDigits << nonceLength))
        ).toString("16");
        
        const signature = crypto.createHash("sha1")
                                .update(`${authConfig.innerSecretKey}${nonce}${timestamp}`)
                                .digest("hex");
    
        return { signature: signature, nonce: nonce, timestamp: timestamp };
    }
    
    static verifyServerToken(signature, nonce, timestamp) {
        if (!policyConfig.bypassTimestampCheck && Date.now() > timestamp + authConfig.expireTime) {
            return false;
        }
    
        const nonceDigits = authConfig.innerSecretKey.match(/\d/g).join('');
        const nonceLength = nonceDigits.length;
        const computedNonce = policyConfig.bypassNonceCheck ? nonce : (
            timestamp + Math.abs(parseInt(nonceDigits << nonceLength))
        ).toString("16");
    
        if (nonce.length != computedNonce.length) {
            return false;
        }
    
        const nonceValidator = crypto.timingSafeEqual(Buffer.from(nonce), Buffer.from(computedNonce));
        if (!nonceValidator) {
            return false;
        }
    
        const computedSignature = crypto.createHash("sha1")
                                        .update(`${authConfig.innerSecretKey}${computedNonce}${timestamp}`)
                                        .digest("hex");
    
        if (signature.length != computedSignature.length) {
            return false;
        }
    
        return crypto.timingSafeEqual(Buffer.from(signature), Buffer.from(computedSignature));
    }
}
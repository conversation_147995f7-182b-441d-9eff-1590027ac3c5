const Responses = require("@common/Responses");
const ParamValidator = require("@common/ParamValidator");
const Redis = require("@common/Redis");
const RedisKeys = require("@common/RedisKeys");
const service = require("@user-service/service");

async function createProfile(request) {
    const nickName = await ParamValidator.checkNickname(request.body.nickName);
    if (!nickName) {
        return Responses.invalidParameter();
    }

    const isNicknameExists = await Redis.exists(RedisKeys.NICKNAME_RESERVATION, nickName);
    if (isNicknameExists) {
        return Responses.nicknameAlreadyExists();
    }
    
    const sex = ParamValidator.checkSex(request.body.sex);
    if (!sex) {
        return Responses.invalidParameter();
    }

    return await service.createProfile(request.getUserId(), nickName, sex);
}

async function changeNickName(request) {
    const newNickname = await ParamValidator.checkNickname(request.query.newName);
    if (!newNickname) {
        return Responses.invalidParameter();
    }

    const isNicknameExists = await Redis.exists(RedisKeys.NICKNAME_RESERVATION, newNickname);
    if (isNicknameExists) {
        return Responses.nicknameAlreadyExists();
    }

    return await service.changeNickName(request.getUserId(), newNickname);
}

async function isChangingNameFree(request) {
    return await service.isChangingNameFree(request.getUserId());
}

async function setUserInfo(request) {
    // TODO: Profile Picture Update
    const details = ParamValidator.checkParamLength(request.body.details);
    if (request.body.details && !details) {
        return Responses.invalidParameter();
    }

    return await service.setUserInfo(request.getUserId(), details);
}

async function getUserInfo(request) {
    return await service.getUserInfo(request.getUserId());
}

async function setUserIcon(request) {
    return await service.setUserIcon(request.getUserId(), request.query.discordId)
}

async function setUserLanguage(request) {
    const language = ParamValidator.checkLanguage(request.query.language);
    if (!language) {
        return Responses.invalidParameter();
    }

    return await service.setUserLanguage(request.getUserId(), language);
}

async function getUserVipInfo(request) {
    return await service.getUserVipInfo(request.getUserId());
}

async function getDailyRewardInfo(request) {
    return await service.getDailyRewardInfo(request.getUserId());
}

async function receiveDailyReward(request) {
    return await service.receiveDailyReward(request.getUserId());
}

async function getDailyTasksAdConfig(request) {
    return await service.getDailyTasksAdConfig(request.getUserId());
}

async function reportUser(request) {
    // TODO: Add parameters
    return await service.reportUser(request.getUserId());
}

async function receiveUserInfoReward(request) {
    return await service.getUserInfoReward(request.getUserId());
}

module.exports = [
    {
        "path": "/user/api/v1/user/register",
        "methods": ["POST"],
        "functions": [createProfile]
    },
    {
        "path": "/user/api/v1/user/nickName",
        "methods": ["PUT"],
        "functions": [changeNickName]
    },
    {
        "path": "/user/api/v1/user/nickName/free",
        "methods": ["GET"],
        "functions": [isChangingNameFree]
    },
    {
        "path": "/user/api/v1/user/info",
        "methods": ["PUT"],
        "functions": [setUserInfo]
    },
    {
        "path": "/user/api/v1/user/icon",
        "methods": ["PUT"],
        "functions": [setUserIcon]
    },
    {
        "path": "/user/api/v1/user/details/info",
        "methods": ["POST"],
        "functions": [getUserInfo]
    },
    {
        "path": "/user/api/v1/user/player/info",
        "methods": ["GET"],
        "functions": [getUserVipInfo]
    },
    {
        "path": "/user/api/v2/users/:userId/daily/sign/in",
        "methods": ["GET", "PUT"],
        "functions": [getDailyRewardInfo, receiveDailyReward]
    },
    {
        "path": "/user/api/v1/users/:userId/daily/tasks/ads/config",
        "methods": ["GET"],
        "functions": [getDailyTasksAdConfig]
    },
    {
        "path": "/user/api/v1/report/push",
        "methods": ["POST"],
        "functions": [reportUser]
    },
    {
        "path": "/user/api/v1/user/language",
        "methods": ["POST"],
        "functions": [setUserLanguage]
    },
    {
        "path": "/user/api/v1/users/prefect/info/reward/:userId",
        "methods": ["POST"],
        "functions": [receiveUserInfoReward]
    }
]
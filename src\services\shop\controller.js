const Responses = require("@common/Responses");
const ParamValidator = require("@common/ParamValidator");
const service = require("@shop-service/service");

async function getDressList(request) {
    const { pageNo, pageSize } = ParamValidator.checkPageParams(request.query.pageNo, request.query.pageSize);
    const currency = ParamValidator.checkCurrency(request.query.currency) ?? 0;
    
    return await service.getDressList(request.getUserId(), parseInt(request.params.typeId), currency, pageNo, pageSize);
}

async function buyDresses(request) {
    let decorationIds = request.query.decorationId ?? [];
    if (!Array.isArray(decorationIds)) {
        decorationIds = [decorationIds];
    }

    decorationIds = decorationIds.map(dressId => parseInt(dressId))
                                 .filter(dressId => !isNaN(dressId));

    if (decorationIds.length == 0) {
        return Responses.invalidParameter();
    }

    return await service.buyDresses(request.getUserId(), decorationIds);
}

async function buyGameProp(request) {
    return await service.buyGameProp(request.getUserId(), request.query.gameId, request.query.propsId);
}

async function buyGame(request) {
    return await service.buyGame(request.getUserId(), request.params.gameId);
}

module.exports = [
    {
        "path": "/shop/api/v1/shop/decorations/buy",
        "methods": ["PUT"],
        "functions": [buyDresses]
    },
    {
        "path": "/shop/api/v1/shop/decorations/:typeId",
        "methods": ["GET"],
        "functions": [getDressList]
    },
    {
        "path": "/shop/api/:version/shop/game/props",
        "methods": ["PUT"],
        "functions": [buyGameProp]   
    },
    {
        "path": "/shop/api/v1/pay/game/:gameId",
        "methods": ["PUT"],
        "functions": [buyGame]
    }
]
const Logger = require("@common/Logger");
const Responses = require("@common/Responses");
const msgConfig = require("@common-config/social");
const Page = require("@common-models/Page");

async function getGroupChatList(userId, pageNo, pageSize) {
    Logger.warn("GetGroupChatList: Implementation needed");
    return Responses.success(Page.empty());
}

async function getGroupChatPrice(userId) {
    return Responses.success({
        ...msgConfig
    });
}

module.exports = {
    getGroupChatList: getGroupChatList,
    getGroupChatPrice: getGroupChatPrice
}
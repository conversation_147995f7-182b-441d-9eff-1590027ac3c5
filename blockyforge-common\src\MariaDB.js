const Logger = require("@common/Logger");
const dbConfig = require("@common-config/database");
const mariadb = require("mariadb");

let pool = null;

function init(dbName) {
    if (dbName) {
        dbConfig["mariadb"]["database"] = dbName;
    }

    pool = mariadb.createPool(dbConfig["mariadb"]);
}

async function executeQuery(query) {
    if (!pool) {
        throw Error("MariaDB is not initialized");
    }

    let conn;
    try {
        conn = await pool.getConnection();
        const result = await conn.query(query);
        return result;
    } catch(err) {
        Logger.error("An error occurred while using MariaDB");
        Logger.error(err);
    } finally {
        if (conn) conn.release();
    }
}

async function findFirst(query, key, defaultValue = null) {
    const data = await executeQuery(query);
    if (!data) {
        return defaultValue;
    }
    
    if (!key) {
        return data[0] ?? defaultValue;
    }

    if (data.length > 0 && data[0][key]) {
        return data[0][key];
    }

    return defaultValue;
}

async function addOrUpdateJsonObject(table, primaryKey, data) {
    data = JSON.stringify(data).replace(/\"/g, "\\\"")
                               .replace(/\'/g, "\\\'");

    await executeQuery(`INSERT INTO ${table} VALUES (${primaryKey},"${data}") ON DUPLICATE KEY UPDATE userId=${primaryKey},data=JSON_MERGE_PATCH(data, "${data}")`);
}

module.exports = {
    init: init,
    executeQuery: executeQuery,
    findFirst: findFirst,
    addOrUpdateJsonObject: addOrUpdateJsonObject
}
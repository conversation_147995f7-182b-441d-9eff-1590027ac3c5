#!/bin/bash

set -euo pipefail

log() {
    echo "[$(date +'%Y-%m-%d %H:%M:%S')] $1"
}

check_installation() {
    if command -v "$1" &>/dev/null; then
        log "$1 installed successfully!"
    else
        log "Error: $1 installation failed!"
        exit 1
    fi
}

########## STEP 0: Switch to Faster Debian Mirror ##########

log "Switching to a faster regional Debian mirror (Germany)..."
sed -i 's|http://deb.debian.org|http://ftp.de.debian.org|g' /etc/apt/sources.list

########## STEP 1: Install Dependencies ##########

log "Updating package lists..."
apt-get update -y

log "Installing curl..."
apt-get install -y curl
check_installation "curl"

log "Installing Node.js (LTS)..."
curl -fsSL https://deb.nodesource.com/setup_22.x | bash -
apt-get install -y nodejs
check_installation "node"
check_installation "npm"

log "Installing Nginx..."
apt-get install -y nginx
check_installation "nginx"

log "Installing tmux..."
apt-get install -y tmux
check_installation "tmux"

log "All required software installed successfully."

########## STEP 2: Clone Repositories ##########

ORG_NAME="Moonsveil"
BRANCH="master"
REPOS=("blockyforge-common" "blockyforge-api" "blockyforge-dispatcher")

log "Creating directory [blockyforge-api-servers]"

cd .. || { log "Failed to go up one directory"; exit 1; }
mkdir -p blockyforge-api-servers
cd blockyforge-api-servers || {
    log "Failed to navigate into [blockyforge-api-servers]"
    exit 1
}

for repo in "${REPOS[@]}"; do
    log "Cloning repository '$repo' from organization '$ORG_NAME' on branch '$BRANCH'..."

    git clone --branch "$BRANCH" --single-branch "**************:$ORG_NAME/$repo.git" || {
        log "Failed to clone $repo"
        exit 1
    }

    cd "$repo" || {
        log "Failed to navigate into repository directory '$repo'"
        exit 1
    }

    log "Running 'npm install' in $repo..."
    npm install || {
        log "Failed to run 'npm install' in $repo"
        exit 1
    }

    cd ..
done

log "(VPS - 1) Setup Successful!"

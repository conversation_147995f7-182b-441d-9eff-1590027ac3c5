const bcrypt = require("bcryptjs");
const Authenticator = require("@common/Authenticator");
const Mailer = require("@common/Mailer");
const Redis = require("@common/Redis");
const RedisKeys = require("@common/RedisKeys");
const Responses = require("@common/Responses");
const MariaDB = require("@common/MariaDB");
const authConfig = require("@common-config/auth");
const policyConfig = require("@common-config/policies");
const DeviceBindTypes = require("@common-constants/DeviceBindTypes");
const MailAddresses = require("@common-constants/MailAddresses");
const MailBindTypes = require("@common/constants/MailBindTypes");
const Account = require("@common-models/Account");
const User = require("@common-models/User");

async function appAuthToken(userId) {
    const account = await Account.fromUserId(userId);
    return Responses.success(account.response());
}

async function appRenew(deviceId) {
    if (policyConfig.disableAccountCreation) {
        return Responses.policyDenyRequest();
    }

    const maxUserId = await MariaDB.findFirst("SELECT MAX(userId) FROM account;", "MAX(userId)", 0);
    const userId = Number(maxUserId) + 16;

    const tokenObject = Authenticator.createUserToken({ userId: userId });
    
    const account = new Account(userId);
    account.setAccessToken(tokenObject.dbToken);
    account.setCreationTime(Date.now());
    await account.save();

    await Redis.setKey({
        key: RedisKeys.USER_CURRENT_DEVICE_ID, params: [account.getUserId()]
    }, deviceId);
    
    // The database contains the database token, we can now overwrite the access token to what will be sent to the client
    account.setAccessToken(tokenObject.userToken);

    return Responses.success({
        userId: account.getUserId(),
        accessToken: account.getAccessToken()
    });
}

async function appSetPassword(userId, password) {
    const account = await Account.fromUserId(userId);
    if (account.getPassword()) {
        return Responses.passwordAlreadySet();
    }

    const cryptoPassword = await bcrypt.hash(password, authConfig.hashSaltRounds);
    account.setPassword(cryptoPassword);
    await account.save();

    return Responses.success();
}

async function appLogin(identifier, password, deviceId) {
    const account = await Account.fromIdentifier(identifier);
    if (!account) {
        return Responses.userNotExists();
    }

    const currentDeviceId = await Redis.getKey(RedisKeys.USER_CURRENT_DEVICE_ID, account.getUserId());
    if (account.getHasDeviceBinding() >= DeviceBindTypes.ENABLED && currentDeviceId && currentDeviceId != deviceId) {
        return Responses.invalidDevice();
    }

    const isPasswordValid = await bcrypt.compare(password, account.getPassword());
    if (!isPasswordValid) {
        return Responses.invalidPassword();
    }

    await Redis.setKey({
        key: RedisKeys.USER_CURRENT_DEVICE_ID,
        params: [account.getUserId()]
    }, deviceId);

    const tokenObject = Authenticator.createUserToken({
        userId: account.userId
    });

    account.setAccessToken(tokenObject.dbToken);
    await account.save();
    
    // The database contains the database token, we can now overwrite the access token to what will be sent to the client
    account.setAccessToken(tokenObject.userToken);

    const isProfileExists = await User.exists(account.getUserId());
    if (!isProfileExists) {
        return Responses.profileNotExists(account.response());
    }

    return Responses.success(account.response());
}

async function modifyPassword(userId, currentPassword, newPassword) {
    const account = await Account.fromUserId(userId);
    
    const isPasswordValid = await bcrypt.compare(currentPassword, account.getPassword());
    if (!isPasswordValid) {
        return Responses.invalidPassword();
    }

    const cryptoPassword = await bcrypt.hash(newPassword, authConfig.saltRounds);
    account.setPassword(cryptoPassword);
    await account.save();
    
    return Responses.success();
}

async function sendEmailCode(userId, email, bindType) {
    const candidate = await Account.fromEmail(email);
    if (candidate && bindType == MailBindTypes.BIND) {
        return Responses.emailAlreadyRegistered();
    }

    const code = Math.floor(100000 + Math.random() * 999999);
    await Redis.setKey({ key: RedisKeys.CACHE_EMAIL_BINDING_CODE, params: [userId] }, code, 600);
    
    switch (bindType) {
        case MailBindTypes.BIND:
            await Redis.setKey({ key: RedisKeys.CACHE_EMAIL_REQUEST, params: [userId] }, email, 600);
            break;
        case MailBindTypes.UNBIND:
            const account = await Account.fromUserId(userId);
            email = account.getEmail();
            break;
    }

    await Mailer.send("Moonsveil TECH", MailAddresses.NO_REPLY, email, `Blockman Forge Email Binding [${code}]`, `Your code is <b>${code}</b>.`);

    return Responses.success();
}

async function bindEmailAddress(userId, verifyCode) {
    const email = await Redis.getKey(RedisKeys.CACHE_EMAIL_REQUEST, userId);
    if (!email) {
        return Responses.emailVerificationError();
    }

    const code = await Redis.getKey(RedisKeys.CACHE_EMAIL_BINDING_CODE, userId);
    if (code != verifyCode) {
        return Responses.emailVerificationError();
    }

    const account = await Account.fromUserId(userId);
    account.setEmail(email);
    await account.save();

    return Responses.success();
}

async function unbindEmailAddress(userId, verifyCode) {
    const code = await Redis.getKey(RedisKeys.CACHE_EMAIL_BINDING_CODE, userId);
    if (code != verifyCode) {
        return Responses.emailVerificationError();
    }

    const account = await Account.fromUserId(userId);
    account.setEmail("");
    await account.save();

    return Responses.success();
}

async function updateDeviceBinding(userId, isEnabled) {
    const account = await Account.fromUserId(userId);

    if (account.getHasDeviceBinding() == DeviceBindTypes.ENFORCED) {
        return Responses.policyDenyRequest();
    }

    if (!account.getEmail()) {
        return Responses.emailNotBound();
    }

    account.setHasDeviceBinding(isEnabled);
    await account.save();

    return Responses.success();
}


module.exports = {
    appAuthToken: appAuthToken,
    appLogin: appLogin,
    appRenew: appRenew,
    appSetPassword: appSetPassword,
    modifyPassword: modifyPassword,
    sendEmailCode: sendEmailCode,
    bindEmailAddress: bindEmailAddress,
    unbindEmailAddress: unbindEmailAddress,
    updateDeviceBinding: updateDeviceBinding
}

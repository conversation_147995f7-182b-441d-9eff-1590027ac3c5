const Responses = require("@common/Responses");
const ParamValidator = require("@common/ParamValidator");
const NoticeTypes = require("@common-constants/NoticeTypes");
const service = require("@game-service/service");
const RankingTypes = require("@common/constants/RankingTypes");

async function getRecentlyPlayedList(request) {
    const language = ParamValidator.checkLanguage(request.headers["language"]);
    if (!language) {
        return Responses.invalidParameter();
    }

    return await service.getRecentlyPlayedList(request.getUserId(), language);
}

async function getRecentlyPlayedFriendList(request) {
    const language = ParamValidator.checkLanguage(request.headers["language"]);
    if (!language) {
        return Responses.invalidParameter();
    }

    return await service.getRecentlyPlayedFriendList(request.getUserId(), language);
}

async function likeGame(request) {
    return await service.likeGame(request.getUserId(), request.params.gameId);
}

async function getGames(request) {
    const { pageNo, pageSize } = ParamValidator.checkPageParams(request.query.pageNo, request.query.pageSize);

    const language = ParamValidator.checkLanguage(request.headers["language"]);
    if (!language) {
        return Responses.invalidParameter();
    }
    
    const orderType = request.query.orderType;
    if (!orderType) {
        return Responses.invalidParameter();
    }

    const typeId = parseInt(request.query.typeId);
    if (isNaN(typeId)) {
        return Responses.invalidParameter();
    }

    return await service.getGames(language, typeId, orderType, pageNo, pageSize);
}

async function gamePartyAuth(request) {
    return await service.gamePartyAuth(request.getUserId());
}

async function getResourceInfo(request) {
    const gameId = request.query.gameType;
    if (!gameId) {
        return Responses.invalidParameter();
    }

    const engineVersion = parseInt(request.query.engineVersion);
    if (!engineVersion) {
        return Responses.invalidParameter();
    }

    const resVersion = parseInt(request.query.resVersion);
    if (isNaN(resVersion)) {
        return Responses.invalidParameter();
    }

    return await service.getResourceInfo(gameId, engineVersion, resVersion);
}

async function getGameDetails(request) {
    const language = ParamValidator.checkLanguage(request.headers["language"]);
    if (!language) {
        return Responses.invalidParameter();
    }
    
    return await service.getGameDetails(request.getUserId(), request.params.gameId, language);
}

async function getGameInformation(request) {
    const language = ParamValidator.checkLanguage(request.params.language);
    if (!language) {
        return Responses.invalidParameter();
    }

    return await service.getGameInformation(request.params.gameId, language);
}

async function getChatRoom(request) {
    return await service.getChatRoom(request.getUserId(), request.query.roomName);
}

async function deleteChatRoom(request) {
    return await service.deleteChatRoom(request.query.roomId);
}

async function getGamesRecommendationByType(request) {
    const { pageNo, pageSize } = ParamValidator.checkPageParams(request.query.pageNo, request.query.pageSize);

    const language = ParamValidator.checkLanguage(request.headers["language"]);
    if (!language) {
        return Responses.invalidParameter();
    }
    
    const type = request.query.type;
    if (!type) {
        return Responses.invalidParameter();
    }

    return await service.getGamesRecommendationByType(language, type, pageNo, pageSize);
}

async function getGameRank(request) {
    const { pageNo, pageSize } = ParamValidator.checkPageParams(request.query.pageNo, request.query.pageSize);

    const gameId = request.params.gameId;
    if (!gameId) {
        return Responses.invalidParameter();
    }

    const type = request.query.type;
    if (type != RankingTypes.WEEK && type != RankingTypes.MONTH && type != RankingTypes.ALL) {
        return Responses.invalidParameter();
    }

    return await service.getGameRank(gameId, type, pageNo, pageSize);
}

async function getUserGameRank(request) {
    const gameId = request.params.gameId;
    if (!gameId) {
        return Responses.invalidParameter();
    }

    const type = request.query.type;
    if (type != RankingTypes.WEEK && type != RankingTypes.MONTH && type != RankingTypes.ALL) {
        return Responses.invalidParameter();
    }

    return await service.getUserGameRank(request.getUserId(), gameId, type);
}

async function getPartyGames(request) {
    const language = ParamValidator.checkLanguage(request.headers["language"]);
    if (!language) {
        return Responses.invalidParameter();
    }
    
    return await service.getPartyGames(request.getUserId(), language);
}

async function getPartyGameConfig(request) {
    return await service.getPartyGameConfig(request.params.gameId);
}

async function getGameTeamMembers(request) {
    return await service.getGameTeamMembers(request.params.teamId);
}

async function getGameTurntableInfo(request) {
    return await service.getGameTurntableInfo(request.params.gameId);
}

async function getGameTurntableReward(request) {
    return await service.getGameTurntableInfo(request.getUserId(), request.params.gameId);
}

async function getGameTurntableRewardName(request) {
    return await service.getGameTurntableRewardName(request.getUserId(), request.params.gameId);
}

async function getEngineInfo(request) {
    const gameId = request.query.gameType;
    if (!gameId) {
        return Responses.invalidParameter();
    }

    const engineVersion = parseInt(request.query.engineVersion);
    if (!engineVersion) {
        return Responses.invalidParameter();
    }

    return await service.getEngineInfo(gameId, engineVersion);
}

async function getGameUpdateInfo(request) {
    const language = ParamValidator.checkLanguage(request.headers["language"]);
    if (!language) {
        return Responses.invalidParameter();
    }

    return await service.getGameUpdateInfo(request.params.gameId, language, parseInt(request.query.engineVersion));
}

async function getGameListUpdateInfo(request) {
    const language = ParamValidator.checkLanguage(request.headers["language"]);
    if (!language) {
        return Responses.invalidParameter();
    }
    
    return await service.getGameListUpdateInfo(request.query.oldEngineVersion, language);
}

async function getCommunityGames(request) {
    const { pageNo, pageSize } = ParamValidator.checkPageParams(request.query.pageNo, request.query.pageSize);
    return await service.getCommunityGames(pageNo, pageSize);
}

async function getCommunityGamesStatus(request) {
    return await service.getCommunityGamesStatus();
}

module.exports = [
    {
        "path": "/game/api/v1/games/playlist/recently",
        "methods": ["GET"],
        "functions": [getRecentlyPlayedList]
    },
    {
        "path": "/game/api/v1/games/playlist/friends",
        "methods": ["GET"],
        "functions": [getRecentlyPlayedFriendList]
    },
    {
        "path": "/game/api/v1/games/:gameId/appreciation",
        "methods": ["PUT"],
        "functions": [likeGame]
    },
    {
        "path": "/game/api/v1/games",
        "methods": ["GET"],
        "functions": [getGames]
    },
    {
        "path": "/game/api/v1/party/auth",
        "methods": ["GET"],
        "functions": [gamePartyAuth]
    },
    {
        "path": "/game/api/v1/games/resource/version",
        "methods": ["GET"],
        "functions": [getResourceInfo]
    },
    {
        "path": "/game/api/v2/games/:gameId",
        "methods": ["GET"],
        "functions": [getGameDetails]
    },
    {
        "path": "/game/api/v1/games/warmup/:gameId/languages/:language",
        "methods": ["GET"],
        "functions": [getGameInformation]
    },
    {
        "path": "/game/api/v1/game/chat/room",
        "methods": ["POST", "DELETE"],
        "functions": [getChatRoom, deleteChatRoom]
    },
    {
        "path": "/game/api/v2/games/recommendation/type",
        "methods": ["GET"],
        "functions": [getGamesRecommendationByType]
    },
    {
        "path": "/game/api/v1/games/:gameId/rank",
        "methods": ["GET"],
        "functions": [getGameRank]
    },
    {
        "path": "/game/api/v1/games/:gameId/uses/rank",
        "methods": ["GET"],
        "functions": [getUserGameRank]
    },
    {
        "path": "/game/api/v1/games/all/open/party",
        "methods": ["GET"],
        "functions": [getPartyGames]
    },
    {
        "path": "/game/api/v1/games/config/app/:gameId",
        "methods": ["GET"],
        "functions": [getPartyGameConfig]
    },
    {
        "path": "/game/api/v1/games/team/member/:teamId",
        "methods": ["GET"],
        "functions": [getGameTeamMembers]
    },
    {
        "path": "/game/api/v1/game/:gameId/turntable",
        "methods": ["GET", "PUT"],
        "functions": [getGameTurntableInfo, getGameTurntableReward] 
    },
    {
        "path": "/game/api/v1/game/:gameId/turntable/props",
        "methods": ["GET"],
        "functions": [getGameTurntableRewardName]
    },
    {
        "path": "/game/api/v1/games/app-engine/upgrade",
        "methods": ["GET"],
        "functions": [getEngineInfo]
    },
    {
        "path": "/game/api/v1/games/update/tip/info/app/:gameId",
        "methods": ["GET"],
        "functions": [getGameUpdateInfo]
    },
    {
        "path": "/game/api/v1/games/update/list/:userId",
        "methods": ["GET"],
        "functions": [getGameListUpdateInfo]
    },
    {
        "path": "/game/api/v1/games/ugc",
        "methods": ["GET"],
        "functions": [getCommunityGames]
    },
    {
        "path": "/game/api/v1/games/ugc/status",
        "methods": ["GET"],
        "functions": [getCommunityGamesStatus]
    }
]
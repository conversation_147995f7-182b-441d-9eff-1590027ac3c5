const mariadb = require("@common/MariaDB");
const Model = require("@common-models/Model");

module.exports = class GameData extends Model {
    constructor() {
        super();

        this.userId = 0;
        this.subKey = 0;
        this.data = "";
    }

    /** @returns {Promise<GameData>} */
    static async fromUserId(userId, tableName, subKey) {
        const gameData = await mariadb.findFirst(`SELECT * FROM ${tableName} WHERE userId=${userId} AND subKey=${subKey}`);
        if (gameData) return Model.fromJson(GameData, gameData);

        const gameDataModel = new GameData();
        gameDataModel.setUserId(userId);
        gameDataModel.setSubKey(subKey);

        return gameDataModel;
    }

    async save(tableName) {
        this.data = this.data.replace(/\"/g, "\\\"")
                             .replace(/\'/g, "\\\'");

        await mariadb.executeQuery(`INSERT INTO ${tableName} VALUES ${super.getSqlCreate()} ON DUPLICATE KEY UPDATE ${super.getSqlUpdate()}`);
    }

    response() {
        return {
            userId: this.userId,
            subKey: this.subKey,
            data: this.data
        }
    }

    setUserId(userId) {
        this.userId = userId;
    }

    getUserId() {
        return this.userId;
    }

    setSubKey(subKey) {
        this.subKey = subKey;
    }

    getSubKey() {
        return this.subKey;
    }

    setData(data) {
        this.data = data;
    }

    getData() {
        return this.data;
    }
}
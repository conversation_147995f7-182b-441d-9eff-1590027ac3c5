CREATE DATABASE bmfdb;
USE bmfdb;

CREATE TABLE account_binding (
    `userId` bigint NOT NULL,
    `connectId` varchar(64) NULL,
    PRIMARY KEY (userId)
) ENGINE=InnoDB CHARSET=utf8mb4;

CREATE TABLE account (
	`userId` bigint NOT NULL,
	`email` varchar(255) NULL,
	`password` varchar(127) NULL,
	`creationTime` bigint(20) NOT NULL,
	`accessToken` varchar(255) NULL,
	`connectId` varchar(64) NOT NULL,
	`hasDeviceBinding` tinyint NOT NULL,
	`banDuration` bigint NOT NULL,
	`banReason` tinyint NOT NULL,
	PRIMARY KEY (userId)
) ENGINE=InnoDB CHARSET=utf8mb4;

CREATE TABLE clan_donation (
    `userId` bigint NOT NULL,
    `clanId` bigint NOT NULL,
    `nickName` varchar(127) NULL,
    `type` int NULL,
    `amount` int NULL,
    `expReward` int NULL,
    `clanGoldReward` int NULL,
    `creationTime` bigint NULL
) ENGINE=InnoDB CHARSET=utf8mb4;

CREATE TABLE clan_member (
    `userId` bigint NOT NULL,
    `clanId` bigint NOT NULL,
    `role` tinyint NULL,
    `experience` bigint NULL,
    PRIMARY KEY (userId)
) ENGINE=InnoDB CHARSET=utf8mb4;

CREATE TABLE clan_message (
    `messageId` bigint AUTO_INCREMENT NOT NULL,
    `userId` bigint NOT NULL,
    `clanId` bigint NOT NULL,
    `authorityId` bigint NULL,
    `message` varchar(63) NULL,
    `picUrl` varchar(127) NULL,
    `nickName` varchar(127) NULL,
    `type` tinyint NULL,
    `status` tinyint NULL,
    `creationTime` bigint NULL,
    PRIMARY KEY (messageId)
) ENGINE=InnoDB CHARSET=utf8mb4;

CREATE TABLE clan_notice (
    `clanId` bigint NOT NULL,
    `content` varchar(1024) NULL,
    PRIMARY KEY (clanId)
) ENGINE=InnoDB CHARSET=utf8mb4;

CREATE TABLE clan (
    `clanId` bigint NOT NULL,
    `name` varchar(63) NULL,
    `picUrl` varchar(127) NULL,
    `tags` json NULL,
    `details` varchar(255) NULL,
    `experience` bigint NULL,
    `level` tinyint NULL,
    `memberCount` tinyint NULL,
    `freeVerify` tinyint NULL,
    `language` varchar(16) NULL,
    `creationTime` bigint NULL,
    PRIMARY KEY (clanId)
) ENGINE=InnoDB CHARSET=utf8mb4;

CREATE TABLE dressing_equipped (
	`userId` bigint NOT NULL,
	`data` json NULL,
    PRIMARY KEY (userId)
) ENGINE=InnoDB CHARSET=utf8mb4;

CREATE TABLE dressing_owned (
	`userId` bigint NOT NULL,
	`data` json NULL,
    PRIMARY KEY (userId)
) ENGINE=InnoDB CHARSET=utf8mb4;

CREATE TABLE friend_request (
    `requestId` bigint AUTO_INCREMENT NOT NULL,
    `userId` bigint NOT NULL,
    `friendId` bigint NOT NULL,
    `message` varchar(63)  NULL,
    `picUrl` varchar(127) NULL,
    `nickName` varchar(127) NULL,
    `sex` tinyint NULL,
    `country` varchar(16) NULL,
    `language` varchar(16) NULl, 
    `status` tinyint NULL,
    `creationTime` bigint NULL,
    PRIMARY KEY (requestId)
) ENGINE=InnoDB CHARSET=utf8mb4;

CREATE TABLE friend (
	`userId` bigint(20) NOT NULL,
	`friendId` bigint(20) NOT NULL,
    `alias` varchar(127) NULL
) ENGINE=InnoDB CHARSET=utf8mb4;

CREATE TABLE game_update (
    `gameId` varchar(16) NOT NULL,
    `version` int NULL,
    `content` varchar(2047) NULL,
    PRIMARY KEY (gameId)
) ENGINE=InnoDB CHARSET=utf8mb4;

CREATE TABLE mailbox_data (
    `mailId` bigint AUTO_INCREMENT NOT NULL,
    `mailType` tinyint NOT NULL,
    `title` varchar(63) NULL,
    `content` varchar(2048) NULL,
    `attachments` json NULL,
    `extraContent` json NULL,
    `creationTime` bigint NULL,
    `status` tinyint NULL,
    PRIMARY KEY (mailId)
) ENGINE=InnoDB CHARSET=utf8mb4;

CREATE TABLE mailbox_record (
	`userId` bigint NOT NULL,
	`data` json null,
	PRIMARY KEY (userId)
) ENGINE=InnoDB CHARSET=utf8mb4;

CREATE TABLE user_locale (
    `userId` bigint NOT NULL,
    `language` varchar(16) NULL,
    `country` varchar(16) NULL,
    PRIMARY KEY (userId)
) ENGINE=InnoDB CHARSET=utf8mb4;

CREATE TABLE user (
	`userId` bigint(20) NOT NULL,
	`nickName` varchar(127) NULL,
	`sex` int NULL,
	`picUrl` varchar(127) NULL,
	`details` varchar(255) NULL,
	`birthday` varchar(16) NULL,
	`isFreeNickname` boolean NULL,
	`donatorTier` tinyint NULL,
	PRIMARY KEY (userId)
) ENGINE=InnoDB CHARSET=utf8mb4;

CREATE TABLE vip (
    `userId` bigint NOT NULL,
    `vip` tinyint NULL,
	`expireDate` bigint NULL,
    `startTime` bigint NULL,
    PRIMARY KEY (userId)
) ENGINE=InnoDB CHARSET=utf8mb4;

CREATE TABLE wealth_record (
	`userId` bigint NOT NULL,
	`created` bigint NULL,
	`currency` int NULL,
	`inoutType` int NULL,
	`orderId` varchar(127) NULL,
	`qty` int NULL,
	`status` int NULL,
	`transactionType` int NULL
) ENGINE=InnoDB CHARSET=utf8mb4;

CREATE TABLE wealth (
	`userId` bigint NULL,
	`golds` int NULL,
	`diamonds` int NULL,
	`clanGolds` int NULL,
    PRIMARY KEY (userId)
) ENGINE=InnoDB CHARSET=utf8mb4;

CREATE TABLE game (
    `gameId` varchar(16) NOT NULL,
    `gameName` varchar(63) NULL,
    `gameTypes` json NULL,
    `likeCount` int NULL,
    `playerCount` int NULL,
    `shopEnabled` boolean NULL,
    `rankEnabled` boolean NULL,
    `partyEnabled` boolean NULL,
    `excludeGame` boolean NULL,
    `authorId` bigint NULL,
    `creationTime` bigint NULL,
    PRIMARY KEY (gameId)
) ENGINE=InnoDB CHARSET=utf8mb4;

INSERT INTO game VALUES ("g1001", "g1001_survival_game",        '["action","adventure"]',                0, 0, 0, 1, 1, 0, 0, 0),
                        ("g1002", "g1002_sky_wars",             '["action","adventure"]',                0, 0, 0, 1, 1, 0, 0, 0),
                        ("g1003", "g1003_team_survival_game",   '["action","adventure"]',                0, 0, 0, 1, 1, 1, 0, 0),
                        ("g1005", "g1005_clan_war",             '["action"]',                            0, 0, 0, 1, 1, 1, 0, 0),
                        ("g1007", "g1007_bow_spleef",           '["action"]',                            0, 0, 0, 1, 1, 0, 0, 0),
                        ("g1008", "g1008_bed_wars",             '["action"]',                            0, 0, 0, 1, 1, 0, 0, 0),
                        ("g1009", "g1009_murder_mystery",       '["action","adventure"]',                0, 0, 0, 1, 1, 0, 0, 0),
                        ("g1010", "g1010_tnt_run",              '["action","adventure"]',                0, 0, 0, 1, 1, 0, 0, 0),
                        ("g1011", "g1011_snowbattle",           '["adventure"]',                         0, 0, 0, 1, 1, 0, 0, 0),
                        ("g1012", "g1012_snowbattle_kill",      '["adventure"]',                         0, 0, 0, 1, 1, 0, 0, 0),
                        ("g1013", "g1013_zombie_infect",        '["adventure","role_play","shooter"]',   0, 0, 0, 1, 1, 0, 0, 0),
                        ("g1014", "g1014_jail_break",           '["role_play","action"]',                0, 0, 0, 1, 1, 0, 0, 0),
                        ("g1015", "g1015_treasure_hunter",      '["business_sim","adventure"]',          0, 0, 0, 1, 1, 0, 0, 0),
                        ("g1016", "g1016_certain_battlefield",  '["shooter"]',                           0, 0, 0, 1, 1, 0, 0, 0),
                        ("g1017", "g1017_hide_and_seek",        '["adventure"]',                         0, 0, 0, 1, 1, 0, 0, 0),
                        ("g1018", "g1018_egg_wars",             '["action"]',                            0, 0, 0, 1, 1, 0, 0, 0),
                        ("g1019", "g1019_alian_attack",         '["adventure","shooter"]',               0, 0, 0, 1, 1, 0, 0, 0),
                        -- ("g1020", "g1020_mini_town",            '["business_sim"]',                      0, 0, 0, 1, 0, 0, 0, 0),
                        ("g1021", "g1021_rainbox_parkour",      '["adventure"]',                         0, 0, 0, 1, 1, 0, 0, 0),
                        ("g1022", "g1022_capture_flag",         '["action"]',                            0, 0, 0, 1, 1, 0, 0, 0),
                        ("g1023", "g1023_build_battle",         '["business_sim"]',                      0, 0, 0, 1, 1, 0, 0, 0),
                        ("g1024", "g1024_gem_knight",           '["action","adventure"]',                0, 0, 0, 1, 1, 0, 0, 0),
                        ("g1025", "g1025_hero_tycoon",          '["action","business_sim","role_play"]', 0, 0, 0, 1, 1, 0, 0, 0),
                        ("g1026", "g1026_tnt_tag",              '["action","adventure"]',                0, 0, 0, 1, 1, 0, 0, 0),
                        ("g1027", "g1027_sky_royale",           '["action","adventure"]',                0, 0, 0, 1, 1, 0, 0, 0),
                        ("g1028", "g1028_ultimate_fighting",    '["action"]',                            0, 0, 0, 1, 1, 0, 0, 0),
                        ("g1029", "g1029_mega_walls",           '["action","adventure"]',                0, 0, 0, 1, 1, 0, 0, 0),
                        ("g1030", "g1030_snow_defender",        '["action"]',                            0, 0, 0, 1, 1, 0, 0, 0),
                        -- ("g1031", "g1031_ranchers",             '["business_sim"]',                      0, 0, 0, 1, 0, 0, 0, 0),
                        ("g1032", "g1032_blockman_strike_hall", '["shooter"]',                           0, 0, 0, 0, 0, 0, 0, 0),
                        ("g1033", "g1033_blockman_strike",      '["shooter"]',                           0, 0, 0, 0, 0, 1, 0, 0),
                        ("g1035", "g1035_ranchers_explore",     '["business_sim"]',                      0, 0, 0, 0, 0, 1, 0, 0),
                        ("g1036", "g1036_ender_vs_slender",     '["action","role_play"]',                0, 0, 0, 1, 1, 0, 0, 0),
                        ("g1046", "g1046_bed_war_hall",         '["action"]',                            0, 0, 0, 0, 0, 1, 0, 0);

CREATE TABLE game_party_config (
    `gameId` varchar(16) NOT NULL,
    `gameType` tinyint NULL,
    `maxPlayers` int NULL,
    `teamNumber` int NULL,
    `teamPlayers` int NULL,
    `vipPlayers` int NULL,
    `commonPlayers` int NULL,
    PRIMARY KEY (gameId)
) ENGINE=InnoDB CHARSET=utf8mb4;

INSERT INTO game_party_config VALUES ("g1001", 0, 24, 0, 0, 24, 5);

CREATE TABLE game_map (
    `gameId` varchar(16) NOT NULL,
    `maxPlayers` int NULL,
    `teamNumber` int NULL,
    `maps` json NULL,
    PRIMARY KEY (gameId)
) ENGINE=InnoDB CHARSET=utf8mb4;

INSERT INTO game_map VALUES ("g1001", 24, 0, '["m1001_1", "m1001_2", "m1001_3", "m1001_4", "m1001_5"]'),
                            ("g1002", 8,  0, '["m1002_1", "m1002_2", "m1002_3", "m1002_4", "m1002_5", "m1002_6", "m1002_7", "m1002_8", "m1002_9"]'),
                            ("g1003", 24, 4, '["m1003_1"]'),
                            ("g1007", 12, 0, '["m1007"]'),
                            ("g1008", 16, 4, '["m1008_1", "m1008_2", "m1008_3", "m1008_4"]'),
                            ("g1009", 12, 0, '["m1009"]'),
                            ("g1010", 8,  0, '["m1010"]'),
                            ("g1011", 8,  2, '["m1011_1", "m1011_2", "m1011_3", "m1011_4"]'),
                            ("g1012", 12, 2, '["m1012"]'),
                            ("g1013", 25, 0, '["m1013"]'),
                            ("g1014", 30, 0, '["m1014"]'),
                            ("g1015", 10, 0, '["m1015"]'),
                            ("g1016", 30, 0, '["m1016"]'),
                            ("g1017", 15, 0, '["m1017"]'),
                            ("g1018", 16, 4, '["m1018_1", "m1018_2", "m1018_3", "m1018_4"]'),
                            ("g1019", 10, 0, '["m1019"]'),
                            ("g1020", 10, 0, '["m1020"]'),
                            ("g1021", 12, 0, '["m1021_1", "m1021_2", "m1021_3", "m1021_4"]'),
                            ("g1022", 16, 2, '["m1022"]'),
                            ("g1023", 8,  0, '["m1023"]'),
                            ("g1024", 32, 4, '["m1024"]'),
                            ("g1025", 10, 0, '["m1025"]'),
                            ("g1026", 30, 0, '["m1026_1", "m1026_2"]'),
                            ("g1027", 16, 4, '["m1027"]'),
                            ("g1028", 16, 0, '["m1028"]'),
                            ("g1029", 16, 4, '["m1029_1", "m1029_2"]'),
                            ("g1030", 12, 2, '["m1030_1", "m1030_2", "m1030_3"]'),
                            ("g1031", 6, 0,  '["m1031"]'),
                            ("g1032", 30, 0, '["m1032"]'),
                            ("g1033", 12, 2, '["m1033"]'),
                            ("g1036", 18, 2, '["m1036_1", "m1036_2"]'),
                            ("g1046", 30, 0, '["m1046"]');
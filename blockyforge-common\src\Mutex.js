module.exports = class Mutex {
    constructor() {
        this._locked = false;
        this._queue = [];
    }

    lock(fn) {
        if (!this._locked) {
            this._locked = true;
            return this._execute(fn);
        }

        return new Promise((resolve, reject) => {
            this._queue.push(() => this._execute(fn).then(resolve, reject));
        });
    }

    async _execute(fn) {
        try {
            return await fn();
        } finally {
            this._unlock();
        }
    }

    _unlock() {
        const next = this._queue.shift();
        if (next) {
            next();
        } else {
            this._locked = false;
        }
    }
};

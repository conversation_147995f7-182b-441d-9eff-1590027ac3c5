const Responses = require("@common/Responses");
const MailStatuses = require("@common-constants/MailStatuses");
const service = require("@mail-service/service");

async function getMailList(request) {
    return await service.getMailList(request.getUserId());
}

async function setMailStatus(request) {
    let mailIds = request.query.ids;
    if (!mailIds) {
        return Responses.invalidParameter();  
    }

    if (Array.isArray(mailIds)) {
        mailIds = mailIds.map(x => parseInt(x)).filter(x => !isNaN(x));
    } else {
        mailIds = parseInt(mailIds);
    }

    const status = parseInt(request.query.status);
    if (status < MailStatuses.READ || status > MailStatuses.DELETE) {
        return Responses.invalidParameter();
    }

    return await service.setMailStatus(request.getUserId(), mailIds, status);
}

async function receiveMailAttachment(request) {
    const mailId = parseInt(request.query.mailId);
    if (isNaN(mailId)) {
        return Responses.mailNotFound();
    }

    return await service.receiveMailAttachment(request.getUserId(), mailId);
}

async function hasNewMail(request) {
    return await service.hasNewMail(request.getUserId());
}

module.exports = [
    {
        "path": "/mailbox/api/v1/mail",
        "methods": ["GET", "PUT"],
        "functions": [getMailList, setMailStatus]
    },
    {
        "path": "/mailbox/api/v1/mail/attachment",
        "methods": ["PUT"],
        "functions": [receiveMailAttachment]
    },
    {
        "path": "/mailbox/api/v1/mail/new",
        "methods": ["GET"],
        "functions": [hasNewMail]
    }
]
const Responses = require("@common/Responses");
const ParamValidator = require("@common/ParamValidator");
const service = require("@decoration-service/service");

async function getDressListByType(request) {
    const { pageNo, pageSize } = ParamValidator.checkPageParams(request.query.pageNo, request.query.pageSize);
    return await service.getDressListByType(request.getUserId(), parseInt(request.params.typeId), pageNo, pageSize);
}

async function getEquippedDresses(request) {
    return await service.getEquippedDresses(request.getUserId());
}

async function getOtherEquippedDresses(request) {
    return await service.getEquippedDresses(parseInt(request.params.otherId));
}

async function useDress(request) {
    const dressId =  parseInt(request.params.decorationId);
    if (isNaN(dressId)) {
        return Responses.invalidParameter();
    }

    return await service.useDress(request.getUserId(), dressId);
}

async function removeDress(request) {
    const dressId =  parseInt(request.params.decorationId);
    if (isNaN(dressId)) {
        return Responses.invalidParameter();
    }

    return await service.removeDress(request.getUserId(), dressId);
}

module.exports = [
    {
        "path": "/decoration/api/v1/decorations/using",
        "methods": ["GET"],
        "functions": [getEquippedDresses]
    },
    {
        "path": "/decoration/api/v:version/decorations/:typeId",
        "methods": ["GET"],
        "functions": [getDressListByType]
    },
    {
        "path": "/decoration/api/v1/decorations/:otherId/using",
        "methods": ["GET"],
        "functions": [getOtherEquippedDresses]
    },
    {
        "path": "/decoration/api/v1/decorations/using/:decorationId",
        "methods": ["PUT", "DELETE"],
        "functions": [useDress, removeDress]
    }
]
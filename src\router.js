const Logger = require("@common/Logger");
const Responses = require("@common/Responses");
const Middlewares = require("@common/Middlewares");
const modules = require("@config/modules");
const requirements = require("@config/requirements");

function init(app) {
    app.all("/", async (request, reply) => {
        reply.send("ARISE");
    });

    for (let i = 0; i < modules.length; i++) {
        const endpoints = require(`@${modules[i]}-service/controller`);
        for (let j = 0; j < endpoints.length; j++) {
            createEndpoint(app, endpoints[j]);
        }
    }

    app.setNotFoundHandler((request, reply) => {
        Logger.warn(`Endpoint may not be implemented: [${request.method}] ${request.url}`);
        const notFound = Responses.notFound();
        reply.code(notFound.status).send(notFound.content);
    });
}

function createEndpoint(app, endpoint) {
    app.route({
        method: ["GET", "POST", "PUT", "DELETE"],
        url: endpoint.path,
        handler: async (request, reply) => {
            try {
                const startTime = Date.now();
                if (!endpoint.methods.includes(request.method)) {
                    Logger.warn(`Endpoint method (${request.method}) may not be implemented: ${request.url}`);
                    const methodNotAllowed = Responses.methodNotAllowed();
                    return reply.code(methodNotAllowed.status).send(methodNotAllowed.content);
                }

                if (!requirements.bypassAuthCheck.includes(endpoint.path)) {
                    const { hasSucceeded, response } = await Middlewares.authenticateUser(request);
                    if (!hasSucceeded) {
                        return reply.code(response.status).send(response.content);
                    }

                    if (!requirements.bypassProfileCheck.includes(endpoint.path)) {
                        const { hasProfile, response } = await Middlewares.checkUserProfile(request);
                        if (!hasProfile) {
                            return reply.code(response.status).send(response.content);
                        }
                    }
                }

                const response = await endpoint.functions[endpoint.methods.indexOf(request.method)](request);
                reply.code(response.status).send(response.content);

                const stopTime = Date.now();
                console.log(`${request.method} [${request.url}] Sent ${response.status} | Delta: ${stopTime - startTime}ms`);
            } catch (e) {
                Logger.error(`Error occurred with API: ${req.url}`);
                Logger.error(e);

                const innerError = Responses.innerError();
                reply.code(innerError.status).send(innerError.content);
            }
        }
    });
}

module.exports = init;
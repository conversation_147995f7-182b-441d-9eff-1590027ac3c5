const Responses = require("@common/Responses");
const service = require("@gamedata-service/service");

async function getUserGamedata(request) {
    const userId = parseInt(request.query.userId);
    if (!userId) {
        return Responses.invalidParameter();
    }

    const tableName = request.query.tableName;
    if (!tableName) {
        return Responses.invalidParameter();
    }

    const subKey = parseInt(request.query.subKey);
    if (!subKey) {
        return Responses.invalidParameter();
    }

    return await service.getUserGamedata(userId, tableName, subKey);
}

async function postUserGamedata(request) {
    const tableName = request.query.tableName;
    if (!tableName) {
        return Responses.invalidParameter();
    }

    const dataPosts = [];

    for (let i = 0; i < request.body.length; i++) {
        const post = request.body[i];

        const userId = parseInt(post.userId);
        if (!userId) {
            return Responses.invalidParameter();
        }
    
        const subKey = parseInt(post.subKey);
        if (!subKey) {
            return Responses.invalidParameter();
        }
        
        const data = typeof(post.data) == "object" ? JSON.stringify(post.data) : post.data.toString();

        dataPosts.push({
            userId, tableName, subKey, data
        });
    }

    if (dataPosts.length == 0) {
        return Responses.invalidParameter();
    }

    return await service.postUserGamedata(tableName, dataPosts);
}

async function getUserGameRank(request) {
    return Responses.success();
}

async function postUserGameRank(request) {
    return Responses.success();
}

async function getGameRankList(request) {
    return Responses.success([]);
}

async function setGameRankExpire(request) {
    return Responses.success();
}

module.exports = [
    {
        "path": "/api/v1/game/data",
        "methods": ["GET", "POST"],
        "functions": [getUserGamedata, postUserGamedata]
    },
    {
        "path": "/api/v1/game/rank",
        "methods": ["GET", "POST"],
        "functions": [getUserGameRank, postUserGameRank]
    },
    {
        "path": "/api/v1/game/rank/list",
        "methods": ["GET"],
        "functions": [getGameRankList]
    },
    {
        "path": "/api/v1/game/rank/expire",
        "methods": ["PUT"],
        "functions": [setGameRankExpire]
    }
]
require("module-alias/register");

require("@common/MariaDB").init();
require("@common/Redis").init();

(async () => {
    const Dressing = require("@common/Dressing");
    await Dressing.init();

    const Heater = require("@common/Heater");
    await Heater.warmupRedis();
})();

const app = require("fastify")({
    logger: false
});

require("./router")(app);

require("@activities/manager").init();

const Logger = require("@common/Logger");

app.listen({ port: 1025, host: "0.0.0.0" }, () => {
    Logger.info(`API server started PORT=1025`);
});
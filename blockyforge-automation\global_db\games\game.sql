CREATE TABLE game (
    `gameId` varchar(16) NOT NULL,
    `gameName` varchar(63) NULL,
    `gameTypes` json NULL,
    `likeCount` int NULL,
    `playerCount` int NULL,
    `shopEnabled` boolean NULL,
    `rankEnabled` boolean NULL,
    `partyEnabled` boolean NULL,
    `excludeGame` boolean NULL,
    `authorId` bigint NULL,
    `creationTime` bigint NULL,
    PRIMARY KEY (gameId)
) ENGINE=InnoDB CHARSET=utf8mb4;

INSERT INTO game VALUES ("g1001", "g1001_survival_game",        '["action","adventure"]',                0, 0, 0, 1, 1, 0, 0, 0),
                        ("g1002", "g1002_sky_wars",             '["action","adventure"]',                0, 0, 0, 1, 1, 0, 0, 0),
                        ("g1003", "g1003_team_survival_game",   '["action","adventure"]',                0, 0, 0, 1, 1, 1, 0, 0),
                        ("g1005", "g1005_clan_war",             '["action"]',                            0, 0, 0, 1, 1, 1, 0, 0),
                        ("g1007", "g1007_bow_spleef",           '["action"]',                            0, 0, 0, 1, 1, 0, 0, 0),
                        ("g1008", "g1008_bed_wars",             '["action"]',                            0, 0, 0, 1, 1, 0, 0, 0),
                        ("g1009", "g1009_murder_mystery",       '["action","adventure"]',                0, 0, 0, 1, 1, 0, 0, 0),
                        ("g1010", "g1010_tnt_run",              '["action","adventure"]',                0, 0, 0, 1, 1, 0, 0, 0),
                        ("g1011", "g1011_snowbattle",           '["adventure"]',                         0, 0, 0, 1, 1, 0, 0, 0),
                        ("g1012", "g1012_snowbattle_kill",      '["adventure"]',                         0, 0, 0, 1, 1, 0, 0, 0),
                        ("g1013", "g1013_zombie_infect",        '["adventure","role_play","shooter"]',   0, 0, 0, 1, 1, 0, 0, 0),
                        ("g1014", "g1014_jail_break",           '["role_play","action"]',                0, 0, 0, 1, 1, 0, 0, 0),
                        ("g1015", "g1015_treasure_hunter",      '["business_sim","adventure"]',          0, 0, 0, 1, 1, 0, 0, 0),
                        ("g1016", "g1016_certain_battlefield",  '["shooter"]',                           0, 0, 0, 1, 1, 0, 0, 0),
                        ("g1017", "g1017_hide_and_seek",        '["adventure"]',                         0, 0, 0, 1, 1, 0, 0, 0),
                        ("g1018", "g1018_egg_wars",             '["action"]',                            0, 0, 0, 1, 1, 0, 0, 0),
                        ("g1019", "g1019_alian_attack",         '["adventure","shooter"]',               0, 0, 0, 1, 1, 0, 0, 0),
                        ("g1020", "g1020_mini_town",            '["business_sim"]',                      0, 0, 0, 1, 0, 0, 0, 0),
                        ("g1021", "g1021_rainbox_parkour",      '["adventure"]',                         0, 0, 0, 1, 1, 0, 0, 0),
                        ("g1022", "g1022_capture_flag",         '["action"]',                            0, 0, 0, 1, 1, 0, 0, 0),
                        ("g1023", "g1023_build_battle",         '["business_sim"]',                      0, 0, 0, 1, 1, 0, 0, 0),
                        ("g1024", "g1024_gem_knight",           '["action","adventure"]',                0, 0, 0, 1, 1, 0, 0, 0),
                        ("g1025", "g1025_hero_tycoon",          '["action","business_sim","role_play"]', 0, 0, 0, 1, 1, 0, 0, 0),
                        ("g1026", "g1026_tnt_tag",              '["action","adventure"]',                0, 0, 0, 1, 1, 0, 0, 0),
                        ("g1027", "g1027_sky_royale",           '["action","adventure"]',                0, 0, 0, 1, 1, 0, 0, 0),
                        ("g1028", "g1028_ultimate_fighting",    '["action"]',                            0, 0, 0, 1, 1, 0, 0, 0),
                        ("g1029", "g1029_mega_walls",           '["action","adventure"]',                0, 0, 0, 1, 1, 0, 0, 0),
                        ("g1030", "g1030_snow_defender",        '["action"]',                            0, 0, 0, 1, 1, 0, 0, 0),
                        ("g1031", "g1031_ranchers",             '["business_sim"]',                      0, 0, 0, 1, 0, 0, 0, 0),
                        ("g1032", "g1032_blockman_strike_hall", '["shooter"]',                           0, 0, 0, 0, 0, 0, 0, 0),
                        ("g1033", "g1033_blockman_strike",      '["shooter"]',                           0, 0, 0, 0, 0, 1, 0, 0),
                        ("g1035", "g1035_ranchers_explore",     '["business_sim"]',                      0, 0, 0, 0, 0, 1, 0, 0),
                        ("g1036", "g1036_ender_vs_slender",     '["action","role_play"]',                0, 0, 0, 1, 1, 0, 0, 0),
                        ("g1046", "g1046_bed_war_hall",         '["action"]',                            0, 0, 0, 0, 0, 1, 0, 0);
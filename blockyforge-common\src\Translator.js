const LanguageKeys = require("@common-constants/LanguageKeys");
const ConfigManager = require("@common/ConfigManager");
const Logger = require("@common/Logger");

module.exports = class Translator {
    static async get(table, key, language) {
        let data = await ConfigManager.get(`lang/${table}/${language}/${key}`);
        if (!data) {
            Logger.warn(`Falling back to ${LanguageKeys.DEFAULT_LANG}`);
            data = await ConfigManager.get(`lang/${table}/${LanguageKeys.DEFAULT_LANG}/${key}`);
        }
    
        return data;
    }
}
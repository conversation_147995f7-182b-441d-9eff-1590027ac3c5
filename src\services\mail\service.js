const Responses = require("@common/Responses");
const Redis = require("@common/Redis");
const RedisKeys = require("@common/RedisKeys");
const ServerTime = require("@common/ServerTime");
const MailAttachmentTypes = require("@common-constants/MailAttachmentTypes");
const MailStatuses = require("@common-constants/MailStatuses");
const MailAttachment = require("@common-models/MailAttachment");
const MailRecord = require("@common-models/MailRecord");
const MailStatus = require("@common-models/MailStatus");
const base = require("@mail-service/base");
const PayService = require("@pay-service/base");
const DecorationService = require("@decoration-service/base");
const UserService = require("@user-service/base");
const TransactionMethods = require("@common-constants/TransactionMethods");

async function getMailList(userId) {
    const mailbox = (await base.getMailbox(userId))
                               .sort((a,b) => b.getCreationTime() - a.getCreationTime())
                               .sort((a,b) => a.getStatus() - b.getStatus())
                               .map(x => x.response());

    return Responses.success(mailbox);
}

async function setMailStatus(userId, mailIds, targetStatus) {
    const mailbox = (await base.getMailbox(userId)).filter(x => {
        if (Array.isArray(mailIds)) { 
            return mailIds.includes(x.getMailId())
        }

        return x.getMailId() == mailIds;
    });

    if (mailbox.length == 0) {
        return Responses.success();
    }

    const statuses = {};
    for (let i = 0; i < mailbox.length; i++) {
        // Can't delete if mail isn't read
        if (mailbox[i].status == MailStatuses.UNREAD && targetStatus == MailStatuses.DELETE) {
            continue;
        }

        // Can't set as read or higher status if attachments aren't claimed 
        if (mailbox[i].status == MailStatuses.UNREAD && mailbox[i].getAttachments().length > 0 && targetStatus > MailStatuses.UNREAD) {
            continue;
        }

        statuses[mailbox[i].getMailId()] = targetStatus;
    }

    await MailRecord.save(userId, statuses);
    return Responses.success();
}

async function receiveMailAttachment(userId, mailId) {
    const mailbox = await base.getMailbox(userId, true);

    let targetMail = null;

    for (let i = 0; i < mailbox.length; i++) {
        if (mailbox[i].mailId == mailId) {
            targetMail = mailbox[i];
            break;
        }
    }
    
    if (!targetMail) {
        return Responses.mailNotFound();
    }

    const mailAttachments = targetMail.getAttachments();
    if (!mailAttachments || mailAttachments.length == 0) {
        return Responses.mailHasNoAttachments();
    }

    for (let i = 0; i < mailAttachments.length; i++) {
        const attachment = MailAttachment.fromJson(mailAttachments[i]);
        switch (attachment.getType()) {
            case MailAttachmentTypes.CURRENCY:
                await PayService.addCurrency(userId, attachment.getItemId(), attachment.getQuantity());
                break;
            case MailAttachmentTypes.DRESS:
                await DecorationService.addDresses(userId, [attachment.getItemId()]);
                break;
            case MailAttachmentTypes.VIP:
                await UserService.addVip(userId, attachment.getVipLevel(), attachment.getVipDays());
                break;
        }
    }
    
    const mailStatus = new MailStatus(targetMail.getMailId(), MailStatuses.READ);
    if (targetMail.getMailId() >= 0) {
        await MailRecord.save(userId, mailStatus.response()); // No need to change the status for VIP mail rewards
    } else {
        await Redis.setKey({ key: RedisKeys.CACHE_VIP_DIAMOND_GIFT, params: [userId] }, "1", ServerTime.getTodayTimeLeft()); // But we obviously still need to know when the person last claimed the VIP rewards
        await Redis.deleteKey(RedisKeys.CACHE_VIP_DIAMOND_GIFT_LEVEL, userId);
    }

    return Responses.success();
}

async function hasNewMail(userId) {
    const mailRecords = await MailRecord.fromUserId(userId);

    let hasNewMail = false;
    for (let i = 0; i < mailRecords.length; i++) {
        if (mailRecords[i].getStatus() == MailStatuses.UNREAD) {
            hasNewMail = true;
            break;
        }
    }

    return Responses.success(hasNewMail);
}

module.exports = {
    getMailList: getMailList,
    setMailStatus: setMailStatus,
    receiveMailAttachment: receiveMailAttachment,
    hasNewMail: hasNewMail
}
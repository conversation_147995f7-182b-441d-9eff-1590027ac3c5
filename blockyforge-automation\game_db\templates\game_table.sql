CREATE TABLE g1014 (
    `userId` bigint NOT NULL,
    `subKey` int NOT NULL,
    `data` text NULL,
    PRIMARY KEY(userId, subKey)
) ENGINE=InnoDB CHARSET=utf8mb4;

CREATE TABLE g1015 (
    `userId` bigint NOT NULL,
    `subKey` int NOT NULL,
    `data` text NULL,
    PRIMARY KEY(userId, subKey)
) ENGINE=InnoDB CHARSET=utf8mb4;

CREATE TABLE g1019 (
    `userId` bigint NOT NULL,
    `subKey` int NOT NULL,
    `data` text NULL,
    PRIMARY KEY(userId, subKey)
) ENGINE=InnoDB CHARSET=utf8mb4;

CREATE TABLE g1021 (
    `userId` bigint NOT NULL,
    `subKey` int NOT NULL,
    `data` text NULL,
    PRIMARY KEY(userId, subKey)
) ENGINE=InnoDB CHARSET=utf8mb4;

CREATE TABLE g1022 (
    `userId` bigint NOT NULL,
    `subKey` int NOT NULL,
    `data` text NULL,
    PRIMARY KEY(userId, subKey)
) ENGINE=InnoDB CHARSET=utf8mb4;

CREATE TABLE g1023 (
    `userId` bigint NOT NULL,
    `subKey` int NOT NULL,
    `data` text NULL,
    PRIMARY KEY(userId, subKey)
) ENGINE=InnoDB CHARSET=utf8mb4;

CREATE TABLE g1025 (
    `userId` bigint NOT NULL,
    `subKey` int NOT NULL,
    `data` text NULL,
    PRIMARY KEY(userId, subKey)
) ENGINE=InnoDB CHARSET=utf8mb4;

CREATE TABLE g1026 (
    `userId` bigint NOT NULL,
    `subKey` int NOT NULL,
    `data` text NULL,
    PRIMARY KEY(userId, subKey)
) ENGINE=InnoDB CHARSET=utf8mb4;

CREATE TABLE g1027 (
    `userId` bigint NOT NULL,
    `subKey` int NOT NULL,
    `data` text NULL,
    PRIMARY KEY(userId, subKey)
) ENGINE=InnoDB CHARSET=utf8mb4;

CREATE TABLE g1028 (
    `userId` bigint NOT NULL,
    `subKey` int NOT NULL,
    `data` text NULL,
    PRIMARY KEY(userId, subKey)
) ENGINE=InnoDB CHARSET=utf8mb4;

CREATE TABLE g1032 (
    `userId` bigint NOT NULL,
    `subKey` int NOT NULL,
    `data` text NULL,
    PRIMARY KEY(userId, subKey)
) ENGINE=InnoDB CHARSET=utf8mb4;

CREATE TABLE g1046 (
    `userId` bigint NOT NULL,
    `subKey` int NOT NULL,
    `data` text NULL,
    PRIMARY KEY(userId, subKey)
) ENGINE=InnoDB CHARSET=utf8mb4;
const ParamValidator = require("@common/ParamValidator");
const Responses = require("@common/Responses");
const friendConfig = require("@common-config/social");
const service = require("@friend-service/service");

async function getFriendList(request) {
    const { pageNo, pageSize } = ParamValidator.checkPageParams(request.query.pageNo, request.query.pageSize);
    return await service.getFriendList(request.getUserId(), pageNo, pageSize);
}

async function getFriendRequestList(request) {
    const { pageNo, pageSize } = ParamValidator.checkPageParams(request.query.pageNo, request.query.pageSize);
    return await service.getFriendRequestList(request.getUserId(), pageNo, pageSize);
}

async function searchFriendByNickname(request) {
    const { pageNo, pageSize } = ParamValidator.checkPageParams(request.query.pageNo, request.query.pageSize);
    return await service.searchFriendByNickname(request.getUserId(), request.params.nickName, pageNo, pageSize);
}

async function searchFriendById(request) {
    const friendId = parseInt(request.params.friendId);
    if (!friendId) {
        return Responses.invalidParameter();
    }

    return await service.searchFriendById(request.getUserId(), friendId);
}

async function sendFriendRequest(request) {
    const friendId = parseInt(request.body.friendId);
    if (!friendId) {
        return Responses.invalidParameter();
    }

    const message = ParamValidator.checkParamLength(request.body.msg, friendConfig.maxRequestLength);
    return await service.sendFriendRequest(request.getUserId(), friendId, message);
}

async function deleteFriend(request) {
    const friendId = parseInt(request.query.friendId);
    if (!friendId) {
        return Responses.invalidParameter();
    }

    return await service.deleteFriend(request.getUserId(), friendId);
}

async function addFriendAlias(request) {
    const friendId = parseInt(request.params.friendId);
    if (!friendId) {
        return Responses.invalidParameter();
    }

    const alias = ParamValidator.checkParamLength(request.query.alias, friendConfig.maxAliasLength);
    if (!alias) {
        return Responses.invalidParameter();
    }

    return await service.addFriendAlias(request.getUserId(), friendId, alias);
}

async function deleteFriendAlias(request) {
    const friendId = parseInt(request.params.friendId);
    if (!friendId) {
        return Responses.invalidParameter();
    }

    return await service.deleteFriendAlias(request.getUserId(), friendId);
}

async function acceptFriendRequest(request) {
    const friendId = parseInt(request.params.friendId);
    if (!friendId) {
        return Responses.invalidParameter();
    }

    return await service.acceptFriendRequest(request.getUserId(), friendId);
}

async function rejectFriendRequest(request) {
    const friendId = parseInt(request.params.friendId);
    if (!friendId) {
        return Responses.invalidParameter();
    }

    return await service.rejectFriendRequest(request.getUserId(), friendId);
}

async function getFriendInfo(request) {
    const friendId = parseInt(request.params.friendId);
    if (!friendId) {
        return Responses.invalidParameter();
    }

    return await service.getFriendInfo(request.getUserId(), friendId);
}

async function getFriendStatus(request) {
    return await service.getFriendStatus(request.getUserId());
}

async function getFriendRecommendation(request) {
    return await service.getFriendRecommendation(request.getUserId());
}

module.exports = [
    {
        "path": "/friend/api/v1/friends",
        "methods": ["GET", "POST", "DELETE"],
        "functions": [getFriendList, sendFriendRequest, deleteFriend]
    },
    {
        "path": "/friend/api/v1/friends/requests",
        "methods": ["GET"],
        "functions": [getFriendRequestList]
    },
    {
        "path": "/friend/api/v1/friends/info/:nickName",
        "methods": ["GET"],
        "functions": [searchFriendByNickname]
    },
    {
        "path": "/friend/api/v1/friends/info/id/:friendId",
        "methods": ["GET"],
        "functions": [searchFriendById]
    },
    {
        "path": "/friend/api/v1/friends/:friendId/alias",
        "methods": ["POST", "DELETE"],
        "functions": [addFriendAlias, deleteFriendAlias]
    },
    {
        "path": "/friend/api/v1/friends/:friendId/agreement",
        "methods": ["PUT"],
        "functions": [acceptFriendRequest]
    },
    {
        "path": "/friend/api/v1/friends/:friendId/rejection",
        "methods": ["PUT"],
        "functions": [rejectFriendRequest]
    },
    {
        "path": "/friend/api/v2/friends/:friendId",
        "methods": ["GET"],
        "functions": [getFriendInfo]
    },
    {
        "path": "/friend/api/v1/friends/status",
        "methods": ["GET"],
        "functions": [getFriendStatus]
    },
    {
        "path": "/friend/api/v1/friends/recommendation",
        "methods": ["GET"],
        "functions": [getFriendRecommendation]
    }
]
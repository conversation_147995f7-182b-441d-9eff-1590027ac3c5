const AssetUtil = require("@common/AssetUtil");
const Redis = require("@common/Redis");
const RedisKeys = require("@common/RedisKeys");
const ServerTime = require("@common/ServerTime");
const vipPrivileges = require("@common-config/privileges");
const Currencies = require("@common-constants/Currencies");
const MailAttachmentTypes = require("@common-constants/MailAttachmentTypes");
const MailStatuses = require("@common-constants/MailStatuses");
const MailTypes = require("@common-constants/MailTypes");
const VipLevels = require("@common-constants/VipLevels");
const VipMailTypes = require("@common-constants/VipMailTypes");
const Mail = require("@common-models/Mail");
const MailAttachment = require("@common-models/MailAttachment");
const MailRecord = require("@common-models/MailRecord");
const Model = require("@common-models/Model");
const Vip = require("@common-models/Vip");

/** @returns {Promise<Mail[]>} */
async function getMailbox(userId, removeReadMails) {
    const mailRecords = await MailRecord.fromUserId(userId);
    const mails = [];
    
    for (let i = 0; i < mailRecords.length; i++) {        
        if (mailRecords[i].getStatus() == MailStatuses.DELETE) {
            continue;
        }

        if (removeReadMails && mailRecords[i].getStatus() == MailStatuses.READ) {
            continue;
        }

        const mailId = mailRecords[i].getMailId();
    
        const mailData = await Mail.fromMailId(mailId);
        if (!mailData) {
            continue;
        }

        mailData.setStatus(mailRecords[i].getStatus());
        mails.push(mailData);
    }

    const rewardMail = await getDiamondGiftMail(userId);
    if (rewardMail) {
        mails.push(rewardMail);
    }

    return mails;
}

async function getDiamondGiftMail(userId) {
    let cacheVipLevel = await Redis.getKey(RedisKeys.CACHE_VIP_DIAMOND_GIFT_LEVEL, userId);
    const vip = await Vip.fromUserId(userId);
    
    if (!cacheVipLevel && vip.getLevel() == VipLevels.NONE) {
        return null;
    }

    const hasClaimedGift = await Redis.getKey(RedisKeys.CACHE_VIP_DIAMOND_GIFT, userId);
    if (hasClaimedGift) {
        return null;
    }

    if (!cacheVipLevel) {
        cacheVipLevel = vip.getLevel();
        await Redis.setKey({
            key: RedisKeys.CACHE_VIP_DIAMOND_GIFT_LEVEL, params: [userId]
        }, cacheVipLevel);
    }

    const attachment = Model.fromJson(MailAttachment, { 
        icon: AssetUtil.getCurrencyIcon(Currencies.DIAMOND),
        itemId: Currencies.DIAMOND,
        quantity: vipPrivileges[cacheVipLevel].dailyDiamondGift,
        type: MailAttachmentTypes.CURRENCY
    });

    const claimDate = new Date(ServerTime.getTodayTimeLeft() * 1000);

    const rewardMail = new Mail();
    rewardMail.setMailId(VipMailTypes[cacheVipLevel]);
    rewardMail.setTitle(`VIP - Daily Gift (${claimDate.getUTCHours()}hour-${claimDate.getUTCMinutes()}mins left)`);
    rewardMail.setContent(`Congratulations! You received a daily gift of ${attachment.getQuantity()} bcubes as part of the VIP program! We wish you can enjoy the journey with your friends!`);
    rewardMail.setCreationTime(Date.now());
    rewardMail.setMailType(MailTypes.SYSTEM);
    rewardMail.setStatus(MailStatuses.UNREAD);
    rewardMail.addAttachment(attachment);

    return rewardMail;
}

module.exports = {
    getMailbox: getMailbox
}
const gameRewardConfig = require("@config/gamereward");
const Model = require("@common/models/Model");

module.exports = class PlayerRecord extends Model {
    constructor() {
        super();

        this.userId = 0;
        this.gameId = "";
        this.recordId = "";
        this.duration = 0;
        this.rank = 0;
        this.startTime = "";
        this.totalNum = 0;
        this.standardNum = 0;
    }

    /** @returns {PlayerRecord} */
    static fromJson(json) {
        return Model.fromJson(PlayerRecord, json);
    }

    getGoldReward() {
        let rewardRatio = gameRewardConfig.rewardRatios[this.rank];
        if (!rewardRatio) {
            rewardRatio = gameRewardConfig.defaultRewardRatio;
        }
    
        let playTimeReward = this.duration * gameRewardConfig.goldPerUnit / gameRewardConfig.timeUnit;
        let rankReward = playTimeReward * rewardRatio * this.totalNum / this.standardNum;
    
        return Math.round(playTimeReward + rankReward);
    }

    setUserId(userId) {
        this.userId = userId;
    }

    getUserId() {
        return this.userId;
    }

    setGameId() {
        this.gameId = gameId;
    }

    getGameId() {
        return this.gameId;
    }

    setRecordId(recordId) {
        this.recordId = recordId;
    }

    getRecordId() {
        return this.recordId;
    }

    setDuration(duration) {
        this.duration = duration;
    }

    getDuration() {
        return this.duration;
    }

    setRank(rank) {
        this.rank = rank;
    }

    getRank() {
        return this.rank;
    }

    setStartTime(startTime) {
        this.startTime = startTime;
    }

    getStartTime() {
        return this.startTime;
    }

    setTotalNum(totalNum) {
        this.totalNum = totalNum;
    }

    getTotalNum() {
        return this.totalNum;
    }

    setStandardNum(stantardNum) {
        this.standardNum = stantardNum;
    }

    getStandardNum() {
        return this.standardNum;
    }
}
const hostConfig = require("@common-config/host");
const cdnConfig = require("@common-config/cdn");
const MailAttachmentTypes = require("@common-constants/MailAttachmentTypes");

function getCurrencyAttachment(currency, quantity) {
    const currencyTypes = {
        "1": "Bcubes",
        "2": "Gold"
    };

    return { 
        name: currencyTypes[currency],
        icon: `${hostConfig.cdnHost}/${cdnConfig.currencyIconPath}/${currency}.png`,
        itemId: currency,
        quantity: quantity,
        type: MailAttachmentTypes.CURRENCY
    };
}

function getDressAttachment(dressId) {
    const DRESS_ID_MIN_LENGTH = 5;
    
    const DressingTypes = {
        "2": "hair",
        "3": "glasses",
        "4": "face",
        "5": "animation",
        "6": "skin",
        "7": "background",
        "8": "top",
        "9": "pants",
        "10": "shoes",
        "11": "hat",
        "13": "scarf",
        "14": "wings",
        "15": "crown"
    };

    const dressTypeId = dressId.toString().substring(0, dressId.toString().length - DRESS_ID_MIN_LENGTH);
    const dressType = DressingTypes[dressTypeId];
    const iconUrl = `${hostConfig.cdnHost}/${cdnConfig.dressIconPath}/${dressType}/${dressId}.png`;

    return {
        icon: iconUrl,
        itemId: dressId,
        type: MailAttachmentTypes.DRESS   
    };
}

function getVipAttachment(vipLevel, vipDays) {
    const vipTypes = {
        "1": "VIP",
        "2": "VIP+",
        "3": "MVP"
    };

    return {
        name: vipTypes[vipLevel],
        icon: `${hostConfig.cdnHost}/${cdnConfig.vipIconPath}/${vipLevel}.png`,
        vipLevel: vipLevel,
        vipDays: vipDays,
        type: MailAttachmentTypes.VIP
    };
}

module.exports = {
    getCurrencyAttachment: getCurrencyAttachment,
    getDressAttachment: getDressAttachment,
    getVipAttachment: getVipAttachment
}
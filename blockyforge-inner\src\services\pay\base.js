const crypto = require("crypto");
const Transaction = require("@common-models/Transaction");
const Wealth = require("@common-models/Wealth");
const TransactionTypes = require("@common-constants/TransactionTypes");
const TransactionResults = require("@common-constants/TransactionResults");
const WealthTypes = require("@common-constants/WealthTypes");

async function addCurrency(userId, currencyType, amount, type) {
    if (!amount) {
        return  { hasFailed: false };
    }
    
    const userWealth = await Wealth.fromUserId(userId);
    const wealthType = WealthTypes[currencyType];

    userWealth[wealthType] += amount;
    await userWealth.save();

    const transaction = new Transaction({
        userId: userId,
        created: Date.now(),
        currency: currencyType,
        inoutType: TransactionTypes.ADDED,
        orderId: crypto.randomBytes(8).toString("hex"), // TODO: Rely on time for Transaction ID generation
        quantity: amount,
        status: TransactionResults.SUCCESS,
        transactionType: type
    });

    await transaction.save();

    return { hasFailed: false, orderId: transaction.orderId, balance: userWealth };
}

async function hasEnoughCurrency(userId, currencyType, amount) {
    if (!amount) {
        return true;
    }

    const userWealth = await Wealth.fromUserId(userId);
    const wealthType = WealthTypes[currencyType];

    return userWealth[wealthType] >= amount;
}

async function removeCurrency(userId, currencyType, amount, type) {
    if (!amount) {
        return { hasFailed: false };
    }

    const userWealth = await Wealth.fromUserId(userId);
    const wealthType = WealthTypes[currencyType];

    amount = Math.floor(amount);
    let currentAmount = userWealth[wealthType];
     
    if (currentAmount - amount < 0) {
        return { hasFailed: true };
    }

    currentAmount -= amount;

    userWealth[wealthType] = currentAmount;
    await userWealth.save();

    const transaction = new Transaction({
        userId: userId,
        created: Date.now(),
        currency: currencyType,
        inoutType: TransactionTypes.REMOVED,
        orderId: crypto.randomBytes(8).toString("hex"), // TODO: Rely on time for Transaction ID generation
        quantity: amount,
        status: TransactionResults.SUCCESS,
        transactionType: type
    });
    
    await transaction.save();
    
    return { hasFailed: false, orderId: transaction.orderId, balance: userWealth };
}

module.exports = {
    addCurrency: addCurrency,
    hasEnoughCurrency: hasEnoughCurrency,
    removeCurrency: removeCurrency
}
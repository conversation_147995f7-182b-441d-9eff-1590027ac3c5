#!/bin/bash

log() {
	echo "[$(date +'%Y-%m-%d %H:%M:%S')] $1"
}

DB_USER="bmfapi_fellownow"
DB_PASS="076eb82e47824f56cba065dd7f191af00b7bfe16"
DB_NAME="bmfapi_fellownow"
DB_HOST="06q42.h.filess.io"
DB_PORT="3305"

SQL_FILE="global_db/setup.sql"

if [ ! -f "$SQL_FILE" ]; then
	log "Error: '$SQL_FILE' not found."
	exit 1
fi

log "Executing setup.sql on MariaDB '$DB_NAME'..."

mysql -u $DB_USER --password=$DB_PASS < $SQL_FILE

log "Global Database setup completed successfully!"
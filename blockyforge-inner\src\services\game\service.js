const PlayerRecord = require("@models/PlayerRecord");
const SettlementRecord = require("@models/SettlementRecord");
const base = require("@game-service/base");
const Responses = require("@common/Responses");

/** @param {PlayerRecord[]} data */
async function getPlayerSettlementRecords(data) {
    const records = [];
    for (let i = 0; i < data.length; i++) {
        const settlement = await base.getPlayerSettlement(data[i].getUserId(), data[i].getGoldReward());
        records.push(settlement);
    }

    return Responses.success(records);
}

async function getPlayerSettlementGold(userId, gold) {
    const settlement = base.getPlayerSettlement(userId, gold);
    return Responses.success(settlement);
}

/** @param {SettlementRecord[]} data */
async function reportSettlementRecords(data) {
    for (let i = 0; i < data.length; i++) {
        await data[i].processForRanking();
        await data[i].processForClan();
    }

    return Responses.success();
}

module.exports = {
    getPlayerSettlementRecords: getPlayerSettlementRecords,
    getPlayerSettlementGold: getPlayerSettlementGold,
    reportSettlementRecords: reportSettlementRecords
}
const Authenticator = require("@common/Authenticator");
const Responses = require("@common/Responses");
const Account = require("@common-models/Account");
const User = require("@common-models/User");

async function authenticateUser(request) {
    const userId = parseInt(request.headers["userid"]);
    const accessToken = request.headers["access-token"];

    if (!userId || !accessToken) {
        return { response: Responses.invalidParameter() };
    }
    
    const account = await Account.fromUserId(userId);
    if (!account) {
        return { response: Responses.authFailed() };
    }

    if (account.getBanDuration() > 0) {
        return { response: Responses.accountBanned(account.getBanInfo()) }
    }

    const { isValid } = Authenticator.verifyUserToken(accessToken, account.getAccessToken());
    
    if (!isValid) {
        return { response: Responses.authFailed() };
    }

    request.getUserId = () => userId;
    return { hasSucceeded: true };
}

function authenticateInner(request) {
    const signature = request.query["signature"];
    const nonce = request.query["nonce"];
    const timestamp = parseInt(request.query["timestamp"]);

    if (!signature || !nonce || !timestamp) {
	    return { response: Responses.notFound() };
    }
    
    const isValid = Authenticator.verifyServerToken(signature, nonce, timestamp);
    if (!isValid) {
        return { response: Responses.notFound() };
    }

    return { hasSucceeded: true };
}

async function checkProfileCheck(request) {
    const isUserExists = await User.exists(request.getUserId());
    if (!isUserExists) {
        return { response: Responses.profileNotExists()  }
    }
    
    return { hasProfile: true };
}

module.exports = {
    authenticateUser: authenticateUser,
    authenticateInner: authenticateInner,
    checkUserProfile: checkProfileCheck
}
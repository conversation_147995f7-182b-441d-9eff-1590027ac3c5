module.exports = {
    CACHE_USER_DONATION_CURRENCY: "cache.donation.%s.%s",
    CACHE_USER_DONATION_TASK: "cache.donation.task.%s",
    
    CACHE_ACTIVITY_FREE_WHEEL: "cache.activity.wheel.free.%s",
    CACHE_ACTIVITY_WHEEL_CURRENCY: "cache.activity.wheel.currency.%s.%s",
    CACHE_ACTIVITY_WHEEL_LUCK: "cache.activity.wheel.luck.%s.%s",

    CACHE_ACTIVITY_LOGIN: "cache.activity.login.%s",
    CACHE_ACTIVITY_LOGIN_HAS_CLAIMED: "cache.activity.login.claimed.%s",

    CACHE_DAILY_LOGIN: "cache.daily.login.%s",
    CACHE_DAILY_LOGIN_HAS_CLAIMED: "cache.daily.login.claimed.%s",
    
    CACHE_VIP_DIAMOND_GIFT: "cache.vip.diamond.gift.%s",
    CACHE_VIP_DIAMOND_GIFT_LEVEL: "cache.vip.diamond.gift.level.%s",
    
    CACHE_GAME_ACCOUNT: "cache.game.token.%s",
    CACHE_USER_SETTLEMENT_GOLD: "cache.user.settlement.gold.%s",

    GAME_PLAYER_COUNT: "game.player.count",
    GAME_LIKE_COUNT: "game.like.count",
    GAME_RELEASE_TIME: "game.release.time",
    GAME_USER_LIKED: "game.liked.%s",

    CACHE_CLAN_WEEK_RANK: "cache.clan.week",
    CACHE_CLAN_MONTH_RANK: "cache.clan.month",
    CLAN_OVERALL_RANK: "clan.overall",

    CACHE_GAME_WEEK_RANK: "cache.game.week.%s",
    CACHE_GAME_MONTH_RANK: "cache.game.month.%s",
    GAME_OVERALL_RANK: "game.overall.%s",

    CACHE_PERSONAL_CLAN_TASK: "cache.personal.clan.task.%s.%s",
    CACHE_PERSONAL_CLAN_SPECIAL_TASK: "cache.personal.special.clan.task.%s",
    CACHE_CLANWIDE_CLAN_TASK: "cache.clanwide.clan.task.%s.%s",
    CACHE_CLANWIDE_USER_CLAIM_TASK: "cache.clanwide.user.claim.task.%s.%s",

    CLAN_TASK_LAST_FREE_REFRESH: "personal.task.last.refresh.%s",

    CACHE_EMAIL_BINDING_CODE: "cache.email.binding.code.%s",
    CACHE_EMAIL_REQUEST: "cache.email.request.%s",

    NICKNAME_RESERVATION: "nickname.reservation.%s",

    USER_CURRENT_DEVICE_ID: "user.current.device.%s"
}
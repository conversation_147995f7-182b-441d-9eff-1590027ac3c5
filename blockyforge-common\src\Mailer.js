const nodemailer = require("nodemailer");
const mailConfig = require("@common-config/mailing");

const transporter = nodemailer.createTransport({
    host: mailConfig.smtpHost,
    port: mailConfig.smtpPort,
    auth: {
        user: mailConfig.username,
        pass: mailConfig.password
    }
});

module.exports = class Mailer {
    static async send(name, sender, receiver, subject, content) {
        return await transporter.sendMail({
            from: { name: name, address: sender },
            to: receiver,
            subject: subject,
            html: content
        });
    }
}
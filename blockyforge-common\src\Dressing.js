const Logger = require("@common/Logger");
const hostConfig = require("@common-config/host");
const cdnConfig = require("@common-config/cdn");
const DressOwnerTypes = require("@common-constants/DressOwnerTypes");
const Dress = require("@common-models/Dress");

const DRESS_ID_MIN_LENGTH = 5;

const DressIds = {
    HAIR: 2,
    GLASSES: 3,
    FACE: 4,
    ACTION: 5,
    SKIN: 6,
    BACKGROUND: 7,
    TOP: 8,
    PANTS: 9,
    SHOES: 10,
    HAT: 11,
    SCARF: 13,
    WINGS: 14,
    CROWN: 15
}

const DressingTypes = {
    "2": "hair",
    "3": "glasses",
    "4": "face",
    "5": "animation",
    "6": "skin",
    "7": "background",
    "8": "top",
    "9": "pants",
    "10": "shoes",
    "11": "hat",
    "13": "scarf",
    "14": "wings",
    "15": "crown"
}

const DressCategories = {
    "8":  1,
    "9":  1,
    "10": 1,
    "2":  2,
    "11": 3,
    "13": 3,
    "14": 3,
    "15": 3,
    "3":  3,
    "4":  4,
    "5":  5,
    "6":  6,
    "7":  7
}

const DressSlaveNames = {
    "2": "custom_hair",
    "3": "custom_glasses",
    "4": "custom_face",
    "5": "animation_idle", // can also be `selectable_action`
    "6": "skin_color",
    "8": "clothes_tops",
    "9": "clothes_pants",
    "10": "custom_shoes",
    "11": "custom_hat",
    "13": "custom_scarf",
    "14": "custom_wing",
    "15": "custom_crown"
}

const DRESS_TYPE_ACTION = "selectable_action";

const dressData = {};

module.exports = class Dressing {
    static async init() {
        for (const typeId of Object.keys(DressCategories)) {
            const categoryId = DressCategories[typeId];
            
            let res = null;
            try {
                res = await fetch(`${hostConfig.cdnUrl}/${cdnConfig.dressConfigPath}/${DressingTypes[typeId]}.json`);
            } catch (err) {
                Logger.error(`(Dressing) Failed at object '${DressingTypes[typeId]}'`);
                Logger.error(err);
                break;
            }
    
            dressData[categoryId] ??= [];
    
            const data = await res.json();
            for (let i = 0; i < data.length; i++) {
                 data[i].iconUrl = data[i].iconUrl.replace("{base}", `${hostConfig.cdnUrl}/${cdnConfig.dressIconPath}`);
            }
    
            dressData[categoryId] = dressData[categoryId].concat(data);
        }
    }
    
    static getDresses(options) {
        if (!dressData[options.categoryId]) {
            return [];
        }
    
        return dressData[options.categoryId].filter((item) => {
            item.status = 0;
            item.hasPurchase = 0;
            
            const sexValidator = item.sex == options.sex || item.sex == 0;
            const currencyValidator = item.currency == options.currency || options.currency == 0;
            const freeValidator = item.currency == 0 && item.price == 0 && (item.vip <= options.vip || item.vip == 0);
            
            if (options.showShopOnly && !item.inShop) {
                return false;
            }
    
            if (options.equippedDresses && options.equippedDresses.includes(item.id)) {
                item.status = 1;
            }
            
            if (options.ownedDresses && options.ownerType == DressOwnerTypes.STRICT) {
                return options.ownedDresses.includes(item.id) || (freeValidator && sexValidator);
            }
    
            if (options.ownedDresses && options.ownerType == DressOwnerTypes.TAG_ITEM && options.ownedDresses.includes(item.id)) {
                item.hasPurchase = 1;
            }
    
            return (sexValidator && currencyValidator);
        });
    }
    
    static getClanDresses(options) {
        if (!dressData[options.categoryId]) {
            return [];
        }
    
        return dressData[options.categoryId].filter((item) => {
            item.status = 0;
            item.hasPurchase = 0;
    
            if (item.clanLevel == 0) {
                return false;
            }
    
            if (options.equippedDresses && options.equippedDresses.includes(item.id)) {
                item.status = 1;
                return true;
            }
            
            if (options.ownedDresses && options.ownerType == DressOwnerTypes.TAG_ITEM && options.ownedDresses.includes(item.id)) {
                item.hasPurchase = 1;
            }
            
            return item.sex == options.sex || item.sex == 0;
        });
    }
    
    /** @returns {Dress} */
    static getDressInfo(dressId) {
        if (!dressId) {
            return null;
        }
        
        dressId = dressId.toString();
        if (dressId.length <= DRESS_ID_MIN_LENGTH) {
            return null;
        }
        
        const dressTypeId = dressId.substring(0, dressId.length - DRESS_ID_MIN_LENGTH);
        const categoryId = DressCategories[dressTypeId];
        if (!dressData[categoryId]) {
            return null;
        }

        const targetElement = dressData[categoryId].find(item => item.id.toString() == dressId);
        if (targetElement) {
            return Dress.fromJson(targetElement);
        }
    
        return null;    
    }

    static getGameDresses(equippedDresses, ownedDresses) {
        const skin = {};
        const userSelectables = [];
    
        for (let i = 0; i < equippedDresses.length; i++) {
            const dressInfo = this.getDressInfo(equippedDresses[i]);
            if (!dressInfo) {
                continue;
            }
            
            if (!DressSlaveNames[dressInfo.typeId]) {
                continue;
            }
    
            if (dressInfo.typeId == DressIds.ACTION && dressInfo.resourceId.includes(DRESS_TYPE_ACTION)) {
                continue;
            }
    
            if (dressInfo.typeId == DressIds.SKIN) {
                skin[DressSlaveNames[dressInfo.typeId]] = dressInfo.resourceId;
            } else {
                skin[DressSlaveNames[dressInfo.typeId]] = dressInfo.resourceId.split('.')[1];
            }
        }
    
        const selectableActions = dressData[DressIds.ACTION.toString()];
        for (let i = 0; i < selectableActions.length; i++) {
            const dressInfo = selectableActions[i];
    
            const isDressFree = dressInfo.currency == 0 && dressInfo.price == 0;
            if (ownedDresses.includes(dressInfo.id) || isDressFree) {
                userSelectables.push(dressInfo.resourceId.split('.')[1]);
            }
        }
    
        skin[DRESS_TYPE_ACTION] = "";

        for (let i = 0; i < userSelectables.length; i++) {
            skin[DRESS_TYPE_ACTION] += userSelectables[i];
    
            if (i + 1 < userSelectables.length) {
                skin[DRESS_TYPE_ACTION] += "-";
            }
        }
    
        return skin;
    }
}
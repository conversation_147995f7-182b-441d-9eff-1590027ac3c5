const Redis = require("@common/Redis");
const RedisKeys = require("@common/RedisKeys");
const Responses = require("@common/Responses")
const ServerTime = require("@common/ServerTime");
const vipPrivileges = require("@common-config/privileges");
const Currencies = require("@common-constants/Currencies");
const Vip = require("@common-models/Vip");
const PayService = require("@pay-service/base");

async function getPlayerSettlement(userId, gold) {
    const vip = await Vip.fromUserId(userId);
    
    const maxGold = vipPrivileges[vip.getLevel()].maxGameGold;
    const goldMultiplier = vipPrivileges[vip.getLevel()].goldMultiplier;

    let currentGold = await Redis.getKey(RedisKeys.CACHE_USER_SETTLEMENT_GOLD, userId) || 0;
    let goldReward = Math.round(gold * goldMultiplier);

    if (maxGold - currentGold < goldReward) {
        goldReward = maxGold - currentGold;
    }

    const { hasFailed } = PayService.addCurrency(userId, Currencies.GOLD, goldReward);
    if (hasFailed) {
        return Responses.failed();
    }
    
    currentGold += goldReward;

    await Redis.setKey({
        key: RedisKeys.CACHE_USER_SETTLEMENT_GOLD, params: [userId]
    }, currentGold, ServerTime.getTodayTimeLeft());

    return {
        userId: userId,
        available: maxGold,
        hasGet: currentGold,
        golds: goldReward
    };
}

module.exports = {
    getPlayerSettlement: getPlayerSettlement
}
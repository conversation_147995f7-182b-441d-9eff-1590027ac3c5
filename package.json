{"name": "blockyforge-api", "main": "src/app.js", "scripts": {"start": "node --max-old-space-size=4092 ."}, "_moduleAliases": {"@common": "./blockyforge-common/src", "@common-constants": "./blockyforge-common/src/constants", "@common-config": "./blockyforge-common/src/config", "@common-models": "./blockyforge-common/src/models", "@constants": "src/constants", "@config": "src/config", "@models": "src/models", "@activities": "src/activities", "@user-service": "src/services/user", "@game-service": "src/services/game", "@account-service": "src/services/account", "@friend-service": "src/services/friend", "@mail-service": "src/services/mail", "@decoration-service": "src/services/decoration", "@pay-service": "src/services/pay", "@clan-service": "src/services/clan", "@shop-service": "src/services/shop", "@msg-service": "src/services/msg", "@activity-service": "src/services/activity", "@discord-service": "src/services/discord", "@config-service": "src/services/config"}, "dependencies": {"bcryptjs": "^2.4.3", "fastify": "^5.1.0", "module-alias": "^2.2.3"}}
#!/bin/bash

set -euo pipefail

log() {
    echo "[$(date +'%Y-%m-%d %H:%M:%S')] $1"
}

check_installation() {
    if command -v "$1" &>/dev/null; then
        log "$1 installed successfully!"
    else
        log "Error: $1 installation failed!"
        exit 1
    fi
}

########## STEP 0: Switch to Faster Debian Mirror ##########

log "Switching to a faster regional Debian mirror (Germany)..."
sed -i 's|http://deb.debian.org|http://ftp.de.debian.org|g' /etc/apt/sources.list

########## STEP 1: Install Dependencies ##########

log "Updating package lists..."
apt-get update -y

log "Installing curl..."
apt-get install -y curl
check_installation "curl"

log "Installing tmux..."
apt-get install -y tmux
check_installation "tmux"

log "Installing ufw (firewall)..."
apt-get install -y ufw
check_installation "ufw"

log "Installing MariaDB..."
apt-get install -y mariadb-server
check_installation "mariadbd"

log "Installing Redis..."
apt-get install -y redis-server
check_installation "redis-server"

log "All required software installed successfully."

########## STEP 2: Configure MariaDB ##########

DB_USER="root"
DB_PASS="bkf2025release"

log "Securing MariaDB..."
mysql_secure_installation <<EOF
y
$DB_PASS
$DB_PASS
y
y
y
y
EOF

log "Setting up MariaDB root access..."
mysql -u root -p"$DB_PASS" -e "ALTER USER '$DB_USER'@'localhost' IDENTIFIED BY '$DB_PASS';"
mysql -u root -p"$DB_PASS" -e "FLUSH PRIVILEGES;"

log "MariaDB configuration complete."

########## STEP 2.1: Run Global Database Setup ##########

chmod +x global_db_setup.sh
chmod +x gamedata_db_setup.sh

GLOBAL_DB_SCRIPT="./global_db_setup.sh"
GAMEDATA_DB_SCRIPT="./gamedata_db_setup.sh"

if [[ -x "$GLOBAL_DB_SCRIPT" ]]; then
    log "Running global database setup..."
    bash "$GLOBAL_DB_SCRIPT"
else
    log "Error: $GLOBAL_DB_SCRIPT not found or not executable."
    exit 1
fi

########## STEP 2.2: Run GameData Database Setup ##########

if [[ -x "$GAMEDATA_DB_SCRIPT" ]]; then
    log "Running gamedata database setup..."
    bash "$GAMEDATA_DB_SCRIPT"
else
    log "Error: $GAMEDATA_DB_SCRIPT not found or not executable."
    exit 1
fi

########## STEP 2.3: Configure MariaDB to Listen on All Interfaces ##########

log "Configuring MariaDB to listen on 0.0.0.0..."

MARIADB_CONF_FILE="/etc/mysql/mariadb.conf.d/50-server.cnf"

# Update the bind-address
sed -i 's/^bind-address\s*=.*/bind-address = 0.0.0.0/' "$MARIADB_CONF_FILE"

# Restart MariaDB
systemctl restart mariadb

log "MariaDB is now listening on 0.0.0.0"

########## STEP 2.4: Allow root access from any host ##########

log "Granting root access from any IP (secured by firewall)..."

mysql -u root -p"$DB_PASS" -e "GRANT ALL PRIVILEGES ON *.* TO 'root'@'%' IDENTIFIED BY '$DB_PASS' WITH GRANT OPTION;"
mysql -u root -p"$DB_PASS" -e "FLUSH PRIVILEGES;"

log "Remote root access granted."


########## STEP 3: Configure Redis ##########

REDIS_CONF="/etc/redis/redis.conf"
log "Configuring Redis with password..."

sed -i 's/^# requirepass .*$/requirepass bkf2025release/' "$REDIS_CONF"
sed -i 's/^bind .*/bind 0.0.0.0/' "$REDIS_CONF"
sed -i 's/^protected-mode .*/protected-mode no/' "$REDIS_CONF"

systemctl restart redis-server
log "Redis configuration complete."

########## STEP 4: Configure UFW Firewall ##########

log "Configuring UFW firewall with full trust for 2 VPS IPs and localhost..."

#                     VPS 1        GameServer VPS
TRUSTED_VPS_IPS=("*************" "***************")

ufw allow ssh

# Allow all traffic from trusted VPS IPs
for ip in "${TRUSTED_VPS_IPS[@]}"; do
    ufw allow from "$ip"
done

# Allow all traffic on localhost
ufw allow from 127.0.0.1

# Deny access to known sensitive ports from everyone else (fallback)
ufw deny 3306/tcp
ufw deny 6379/tcp
ufw deny 7560/tcp
ufw deny 7561/tcp

# Default policy: deny all incoming, allow outgoing
ufw default deny incoming
ufw default allow outgoing

# Enable UFW
ufw --force enable

log "UFW firewall enabled with full access for 2 trusted VPS IPs and localhost. All other incoming connections blocked."

#############################################

log "(VPS 2) Setup complete."

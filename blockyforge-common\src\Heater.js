const MariaDB = require("@common/MariaDB");
const Redis = require("@common/Redis");
const RedisKeys = require("@common/RedisKeys");
const warmupConfig = require("@common-config/warmup");
const lanuageConfig = require("@common-config/full_languages");
const ConfigManager = require("@common/ConfigManager");
const Logger = require("@common/Logger");

module.exports = class Heater {
    static async warmupConfigs() {
        const startTime = Date.now();
        
        const games = await MariaDB.executeQuery(`SELECT * FROM game`);
        const langPromises = [];

        for (const lang of lanuageConfig) {
            langPromises.push(
                ...games.filter(game => !game.excludeGame).map(game => 
                    ConfigManager.get(`lang/games/${lang}/${game.gameId}`)
                ),
                ConfigManager.get(`lang/games/${lang}/tags`),
                ConfigManager.get(`lang/clan/${lang}/tasks`)
            );
        }

        await Promise.all(langPromises);
        
        const warmupPromises = warmupConfig.map(config => ConfigManager.get(config));
        await Promise.all(warmupPromises);
        
        const gamePromises = games.map(game => ConfigManager.get(`${game.gameId}`));
        await Promise.all(gamePromises);

        const stopTime = Date.now();
        Logger.info(`Warmup completed | Delta: ${(stopTime - startTime) / 1000}s`);
    }
    
    static async warmupRedis() {
        const games = await MariaDB.executeQuery(`SELECT * FROM game`);
        const redisPromises = [];

        for (const game of games) {
            if (game.excludeGame) {
                continue; // Skip excluded games
            }

            redisPromises.push(
                Redis.getKeyScore(RedisKeys.GAME_LIKE_COUNT, game.gameId).then(likeCount => {
                    if (!likeCount) {
                        return Redis.incrementKeyScore(RedisKeys.GAME_LIKE_COUNT, game.gameId, 0);
                    }
                }),
                Redis.getKeyScore(RedisKeys.GAME_RELEASE_TIME, game.gameId).then(releaseTime => {
                    if (!releaseTime) {
                        return Redis.incrementKeyScore(RedisKeys.GAME_RELEASE_TIME, game.gameId, game.creationTime);
                    }
                }),
                Redis.getKeyScore(RedisKeys.GAME_PLAYER_COUNT, game.gameId).then(playerCount => {
                    if (!playerCount) {
                        return Redis.incrementKeyScore(RedisKeys.GAME_PLAYER_COUNT, game.gameId, 0);
                    }
                })
            );
        }

        await Promise.all(redisPromises);
    }
}
const hostConfig = require("@common-config/host");
const cdnConfig = require("@common-config/cdn");

module.exports = class AssetUtil {
    static getVipIcon(vipLevel) {
        return `${hostConfig.cdnUrl}/${cdnConfig.vipIconPath}/${vipLevel}.png`;
    }
    
    static getCurrencyIcon(currency) {
        return `${hostConfig.cdnUrl}/${cdnConfig.currenyPath}/${currency}.png`;
    }

    static getGameCover(gameId) {
        return `${hostConfig.cdnUrl}/${cdnConfig.gameIconPath}/${gameId}.png`;
    }

    static getGameResUrl(gameId) {
        return `${hostConfig.cdnUrl}/${cdnConfig.gameResPath}/${gameId}.zip`;
    }

    static getGameMapUrl(mapName) {
        return `${hostConfig.mapUrl}/${cdnConfig.mapPath}/${mapName}.zip`;
    }
}
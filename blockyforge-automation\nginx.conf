upstream config_service {
    round-robin;
    server localhost:1024;
}

upstream api_service {
    round-robin;
    server localhost:1025;
}

upstream dispatch_service {
    round-robin;
    server localhost:1026;
}

server {
    listen 80;

    location /config/files/ {
        proxy_pass http://config_service;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        # proxy_set_header X-Service-Name "config-service";
    }

    location / {
        proxy_pass http://api_service;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        # proxy_set_header X-Service-Name "api-service";
    }

    location /v1/dispatch {
        proxy_pass http://dispatch_service;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        # proxy_set_header X-Service-Name "dispatch-service";
    }
    
    # timeouts and buffers for better handling of service responses
    proxy_connect_timeout 30s;
    proxy_send_timeout 30s;
    proxy_read_timeout 60s;
    
    # proxy_buffer_size 16k;
    # proxy_buffers 4 32k;
    # proxy_busy_buffers_size 64k;
    # proxy_max_temp_file_size 0;
    
    # access_log /var/log/nginx/api_service_access.log main;
}

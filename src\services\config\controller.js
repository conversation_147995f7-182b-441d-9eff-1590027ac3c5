const Responses = require("@common/Responses");
const service = require("@config-service/service");

async function getBlockmodsConfig(request) {
    return await service.getBlockmodsConfig();
}

async function getBlockymodsCheckVersion(request) {
    return await service.getBlockymodsCheckVersion();
}

async function getBlockymodsCheckRes(request) {
    return await service.getBlockymodsCheckRes();
}

async function getIndiegameCheckVersion(request) {
    return await service.getIndiegameCheckVersion();
}

async function getIndiegameConfig(request) {
    return await service.getIndiegameConfig();
}

module.exports = [
    {
        "path": "/config/files/blockmods-config",
        "methods": ["GET"],
        "functions": [getBlockmodsConfig]
    },
    {
        "path": "/config/files/blockymods-check-version",
        "methods": ["GET"],
        "functions": [getBlockymodsCheckVersion]
    },
    {
        "path": "/config/files/blockymods-check-res",
        "methods": ["GET"],
        "functions": [getBlockymodsCheckRes]
    },
    {
        "path": "/config/files/indiegame-check-version",
        "methods": ["GET"],
        "functions": [getIndiegameCheckVersion]
    },
    {
        "path": "/config/files/indiegame-config",
        "methods": ["GET"],
        "functions": [getIndiegameConfig]
    }
];

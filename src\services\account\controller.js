const service = require("@account-service/service");
const ParamValidator = require("@common/ParamValidator");
const Responses = require("@common/Responses");
const DeviceBindTypes = require("@common-constants/DeviceBindTypes");
const MailBindTypes = require("@common-constants/MailBindTypes");

async function appAuthToken(request) {
    return await service.appAuthToken(request.getUserId());
}

async function appRenew(request) {
    const deviceId = ParamValidator.checkSha1(request.headers["bmg-device-id"]);
    if (!deviceId) {
       return Responses.invalidParameter();
    }

    return await service.appRenew(deviceId);
}

async function appSetPassword(request) {
    const password = ParamValidator.checkPassword(request.body.password);
    if (!password) {
        return Responses.invalidParameter();
    }

    return await service.appSetPassword(request.getUserId(), password);
}

async function appLogin(request) {
    const identifier = request.body.uid;
    if (!identifier) {
        return Responses.invalidParameter();
    }

    const password = ParamValidator.checkPassword(request.body.password);
    if (!password) {
	    return Responses.invalidParameter();
    }

    const deviceId = ParamValidator.checkSha1(request.headers["bmg-device-id"]);
    if (!deviceId) {
        return Responses.invalidParameter();
    }
    
    return await service.appLogin(identifier, password, deviceId);
}

async function userLogout(request) {
    return await service.userLogout(request.getUserId());
}

async function modifyPassword(request) {
    const oldPassword = ParamValidator.checkPassword(request.body.oldPassword);
    if (!oldPassword) {
        return Responses.invalidParameter();
    }

    const newPassword = ParamValidator.checkPassword(request.body.newPassword);
    if (!newPassword) {
        return Responses.invalidParameter();
    }

    return await service.modifyPassword(request.getUserId(), oldPassword, newPassword);
}

async function sendEmailCode(request) {
    const bindType = parseInt(request.query.isBind);
    if (bindType != MailBindTypes.BIND && bindType != MailBindTypes.UNBIND) {
        return Responses.invalidParameter();
    }
    
    const email = ParamValidator.checkEmail(request.body.email);
    if (bindType == MailBindTypes.BIND && !email) {
        return Responses.invalidParameter();
    }

    return await service.sendEmailCode(request.getUserId(), email, bindType);
}

async function bindEmailAddress(request) {
    const verifyCode = parseInt(request.query.verifyCode);
    if (!verifyCode) {
        return Responses.invalidParameter();
    }

    return await service.bindEmailAddress(request.getUserId(), verifyCode);
}

async function unbindEmailAddress(request) {
    const verifyCode = parseInt(request.query.verifyCode);
    if (!verifyCode) {
        return Responses.invalidParameter();
    }

    return await service.unbindEmailAddress(request.getUserId(), verifyCode);
}

async function updateDeviceBinding(request) {
    const isEnabled = parseInt(request.query.isEnabled);
    if (isNaN(isEnabled)) {
        return Responses.invalidParameter();
    }

    if (isEnabled != DeviceBindTypes.DISABLED && isEnabled != DeviceBindTypes.ENABLED) {
        return Responses.invalidParameter();
    }

    return await service.updateDeviceBinding(request.getUserId(), isEnabled);
}

module.exports = [
    {
        "path": "/user/api/v1/app/auth-token",
        "methods": ["GET"],
        "functions": [appAuthToken]
    },
    {
        "path": "/user/api/v1/app/renew",
        "methods": ["POST"],
        "functions": [appRenew]
    },
    {
        "path": "/user/api/v1/app/set-password",
        "methods": ["POST"],
        "functions": [appSetPassword]
    },
    {
        "path": "/user/api/v1/app/login",
        "methods": ["POST"],
        "functions": [appLogin]
    },
    {
        "path": "/user/api/v1/login-out",
        "methods": ["PUT"],
        "functions": [userLogout]
    },
    {
        "path": "/user/api/v1/user/password/modify",
        "methods": ["POST"],
        "functions": [modifyPassword]
    },
    {
        "path": "/user/api/v1/email/send",
        "methods": ["POST"],
        "functions": [sendEmailCode]
    },
    {
        "path": "/user/api/v1/email/bind",
        "methods": ["POST", "DELETE"],
        "functions": [bindEmailAddress, unbindEmailAddress]
    },
    {
        "path": "/user/api/v1/user/device/binding",
        "methods": ["POST"],
        "functions": [updateDeviceBinding]
    }
]
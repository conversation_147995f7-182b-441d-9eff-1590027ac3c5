const logger = require("@common/Logger");
const Responses = require("@common/Responses");
const recharge = require("@config/recharge");
const firstRechargeConfig = require("@config/firstrecharge");
const Transaction = require("@common-models/Transaction");
const Wealth = require("@common-models/Wealth");

async function getUserWealth(userId) {
    const wealth = await Wealth.fromUserId(userId);
    return Responses.success(wealth);
}

async function getWealthRecord(userId, pageNo, pageSize) {
    const transactions = await Transaction.fromUserId(userId, pageNo, pageSize);
    return Responses.success(transactions);
}

async function getFirstTopUpReward(userId) {
    return Responses.success(firstRechargeConfig);
}

async function getTopUpProducts(userId, type) {
    logger.warn("GetTopUpProducts: Implementation needed");
    return Responses.success(recharge);
}

async function getVipProducts(userId) {
    logger.warn("GetVipProducts: Implementation needed");
    return Responses.innerError();
}

async function getShowThirdPartyPayMethods(userId) {
    return Responses.success(false);
}

module.exports = {
    getUserWealth: getUserWealth,
    getWealthRecord: getWealthRecord,
    getFirstTopUpReward: getFirstTopUpReward,
    getTopUpProducts: getTopUpProducts,
    getVipProducts: getVipProducts,
    getShowThirdPartyPayMethods: getShowThirdPartyPayMethods
}
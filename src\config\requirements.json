{"bypassAuthCheck": ["/user/api/v1/app/renew", "/user/api/v1/app/login", "/game/api/v2/games/recommendation/type", "/game/api/v1/games", "/config/files/blockmods-config", "/config/files/blockymods-check-version", "/config/files/blockymods-check-res", "/config/files/indiegame-check-version", "/config/files/indiegame-config"], "bypassProfileCheck": ["/user/api/v1/login-out", "/user/api/v1/app/set-password", "/user/api/v1/user/password/modify", "/user/api/v1/user/register", "/user/api/v1/user/device/binding"]}
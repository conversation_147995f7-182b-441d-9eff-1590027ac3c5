const service = require("@activity-service/service");
const Responses = require("@common/Responses");
const WheelTypes = require("@common-constants/WheelTypes");

async function getSignInActivity(request) {
    return await service.getSignInActivity(request.getUserId());
}

async function receiveSignInReward(request) {
    return await service.receiveSignInReward(request.getUserId());
}

async function getActivityList(request) {
    return await service.getActivityList(request.getUserId());
}

async function getActivityTaskList(request) {
    return await service.getActivityTaskList(request.getUserId(), request.query.titleType);
}

async function getActivityFreeWheelStatus(request) {
    const activityId = request.query.activityId;
    if (!activityId) {
        return Responses.invalidParameter();
    }
    
    return await service.getActivityFreeWheelStatus(request.getUserId(), activityId);
}

async function getActivityWheelInfo(request) {
    const activityId = request.query.activityId;
    if (!activityId) {
        return Responses.invalidParameter();
    }

    const type = request.query.type;
    if (type != WheelTypes.GOLD && type != WheelTypes.DIAMOND) {
        return Responses.invalidParameter();
    }

    return await service.getActivityWheelInfo(request.getUserId(), activityId, type);
}

async function getActivityWheelShopInfo(request) {
    const activityId = request.query.activityId;
    if (!activityId) {
        return Responses.invalidParameter();
    }

    const type = request.query.type;
    if (type != WheelTypes.GOLD && type != WheelTypes.DIAMOND) {
        return Responses.invalidParameter();
    }

    return await service.getActivityWheelShopInfo(request.getUserId(), activityId, type);
}

async function playActivityWheel(request) {
    const activityId = request.query.activityId;
    if (!activityId) {
        return Responses.invalidParameter();
    }

    const type = request.query.type;
    if (type != WheelTypes.GOLD && type != WheelTypes.DIAMOND) {
        return Responses.invalidParameter();
    }

    const isMultiplePlay = parseInt(request.query.isMulti);
    if (isMultiplePlay != 0 && isMultiplePlay != 1) {
        return Responses.invalidParameter();
    }

    return await service.playActivityWheel(request.getUserId(), activityId, type, isMultiplePlay);
}

async function exchangeBlock(request) {
    const activityId = request.query.activityId;
    if (!activityId) {
        return Responses.invalidParameter();
    }

    const type = request.query.type;
    if (type != WheelTypes.GOLD && type != WheelTypes.DIAMOND) {
        return Responses.invalidParameter();
    }

    const rewardId = parseInt(request.query.rewardId);
    if (isNaN(rewardId)) {
        return Responses.invalidParameter();
    }

    return await service.exchangeBlock(request.getUserId(), activityId, type, rewardId);
}

module.exports = [
    {
        "path": "/activity/api/v1/signIn",
        "methods": ["GET", "POST"],
        "functions": [getSignInActivity, receiveSignInReward]
    },
    {
        "path": "/activity/api/v1/activity/title",
        "methods": ["GET"],
        "functions": [getActivityList]
    },
    {
        "path": "/activity/api/v1/activity/action",
        "methods": ["GET"],
        "functions": [getActivityTaskList]
    },
    {
        "path": "/activity/api/v1/lucky/turntable/gold/status",
        "methods": ["GET"],
        "functions": [getActivityFreeWheelStatus]
    },
    {
        "path": "/activity/api/v1/lucky/turntable",
        "methods": ["GET", "POST"],
        "functions": [getActivityWheelInfo, playActivityWheel]
    },
    {
        "path": "/activity/api/v1/lucky/turntable/block/reward",
        "methods": ["GET"],
        "functions": [getActivityWheelShopInfo]
    },
    {
        "path": "/activity/api/v1/lucky/turntable/block/reward/:blockNeeded",
        "methods": ["POST"],
        "functions": [exchangeBlock]
    }
]
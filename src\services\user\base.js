const Model = require("@common-models/Model");
const Vip = require("@common-models/Vip");

/** @returns {Promise<{before: Vip, now: Vip}>} */
async function addVip(userId, vipLevel, days) {
    const userVip = await Vip.fromUserId(userId);
    const oldVip = Model.clone(Vip, userVip);

    userVip.upgrade(vipLevel);
    userVip.addDays(days);
    await userVip.save();

    return { before: oldVip, now: userVip };
}

module.exports = {
    addVip: addVip
}
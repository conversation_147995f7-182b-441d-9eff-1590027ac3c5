const util = require("node:util");
const Redis = require("redis");
const dbConfig = require("@common-config/database");
const logger = require("@common/Logger");
const Page = require("@common-models/Page");

let client = null;

function init() {
    client = Redis.createClient(dbConfig["redis"]);
    
    client.on("error", (err) => {
        logger.error("An error occurred while using Redis");
        logger.error(err);
    });

    client.connect();
}

function instance() {
    if (!client) {
        throw Error("Redis is not initialized");
    }

    return client;
}

async function getKey(format, ...params) {
    if (!client) {
        throw Error("Redis is not initialized");
    }

    const key = util.format(format, ...params ?? []);
    
    let value = await client.get(key);
    if (value && !isNaN(value)) {
        value = parseInt(value);
    }

    return value;
}

async function setKey(format, data, expireDate) {
    if (!client) {
        throw Error("Redis is not initialized");
    }

    const key = util.format(format.key, ...format.params ?? []);
    await client.set(key, data);
    
    const currExpireDate = await client.ttl(key);
    if (currExpireDate == -1 && expireDate && expireDate > 0) {
        await client.expire(key, expireDate)
    }
}

async function exists(format, ...params) {
    if (!client) {
        throw Error("Redis is not initialized");
    }

    const key = util.format(format, ...params);
    return await client.exists(key);
}

async function incrementKeyScore(keySet, member, value) {
    if (!client) {
        throw Error("Redis is not initialized");
    }

    await client.zIncrBy(keySet, value, member.toString()); 
}

async function getKeyScore(keySet, member) {
    if (!client) {
        throw Error("Redis is not initialized");
    }

    let value = await client.zScore(keySet, member.toString());
    if (value && !isNaN(value)) {
        value = parseInt(value);
    }

    return value; 
}

async function getKeyRank(keySet, member, isReverse) {
    if (!client) {
        throw Error("Redis is not initialized");
    }

    if (isReverse) {
        return await client.zRevRank(keySet, member.toString());
    }

    return await client.zRank(keySet, member.toString());
}

async function getSetSize(keySet) {
    if (!client) {
        throw Error("Redis is not initialized");
    }

    return await client.zCard(keySet);
}

async function getScoreList(keySet, pageNo, pageSize, isReverse) {
    if (!client) {
        throw Error("Redis is not initialized");
    }

    const startIndex = Page.getStartIndex(pageNo, pageSize);
    return await client.zRangeWithScores(keySet, startIndex, startIndex + pageSize - 1, { "REV": isReverse });
}

async function deleteKeyScore(keySet, member) {
    if (!client) {
        throw Error("Redis is not initialized");
    }

    await client.zRem(keySet, member.toString());
}

async function setExpire(format, expireDate) {
    if (!client) {
        throw Error("Redis is not initialized");
    }

    const key = util.format(format.key, ...format.params ?? []);
    const currExpireDate = await client.ttl(key);
    if (currExpireDate == -1 && expireDate && expireDate > 0) {
        await client.expire(key, expireDate)
    }
}

async function getExpire(format, ...params) {
    if (!client) {
        throw Error("Redis is not initialized");
    }

    const key = util.format(format, ...params ?? []);
    return await client.ttl(key);
}

async function deleteKey(format, ...params) {
    if (!client) {
        throw Error("Redis is not initialized");
    }

    const key = util.format(format, ...params ?? []);
    await client.del(key);
}

module.exports = {
    init: init,
    instance: instance,
    getKey: getKey,
    setKey: setKey,
    incrementKeyScore: incrementKeyScore,
    getKeyScore: getKeyScore,
    getKeyRank: getKeyRank,
    getSetSize: getSetSize,
    getScoreList: getScoreList,
    deleteKeyScore: deleteKeyScore,
    exists: exists,
    setExpire: setExpire,
    getExpire: getExpire,
    deleteKey: deleteKey
}
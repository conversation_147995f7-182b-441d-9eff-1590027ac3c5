module.exports = class Logger {
    static log(color, type, message) {
        console.log(color, `${new Date().toLocaleString()} [${type.toUpperCase()}] ${message}`);
    }
    
    static debug(value) {
        this.log("\x1b[92m%s\x1b[0m", "debug", value);
    }
    
    static info(value) {
        this.log("\x1b[94m%s\x1b[0m", "info", value);
    }
    
    static warn(value) {
        this.log("\x1b[93m%s\x1b[0m", "warn", value);
    }
    
    static error(value) {
        this.log("\x1b[91m%s\x1b[0m", "error", value);
        if (typeof(value) == "object") {
            this.log("", "STACKTRACE", value.stack);
        }
    }
    
    static fatal(value) {
        this.log("\x1b[31m%s\x1b[0m", "fatal", value);
        if (typeof(value) == "object") {
            this.log("", "STACKTRACE", value.stack);
        }
    }
}
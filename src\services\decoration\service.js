const Dressing = require("@common/Dressing");
const Responses = require("@common/Responses");
const DressOwnerTypes = require("@common-constants/DressOwnerTypes");
const base = require("@decoration-service/base");
const Clan = require("@common-models/Clan");
const ClanMember = require("@common-models/ClanMember");
const DressOptions = require("@common-models/DressOptions");
const Page = require("@common-models/Page");
const User = require("@common-models/User");
const Vip = require("@common-models/Vip");

async function getDressListByType(userId, categoryId, pageNo, pageSize) {
    const ownedDresses = await base.getOwnedDresses(userId);
    const usingDresses = await base.getEquippedDresses(userId);

    if (ownedDresses.length == 0) {
        Responses.success([]);
    }

    const user = await User.fromUserId(userId);
    const vip = await Vip.fromUserId(userId);

    const filteredDresses = Dressing.getDresses(new DressOptions({
        categoryId: categoryId,
	    sex: user.getSex(),
        vip: vip.getLevel(),
        ownedDresses: ownedDresses,
        equippedDresses: usingDresses,
        ownerType: DressOwnerTypes.STRICT
    }));

    const data = new Page(filteredDresses, filteredDresses.length, pageNo, pageSize);
    return Responses.success(data);
}

async function getEquippedDresses(targetId) {
    const equippedDressesInfo = [];

    const equippedDresses = await base.getEquippedDresses(targetId);
    for (let i = 0; i < equippedDresses.length; i++) {
        const dressInfo = Dressing.getDressInfo(equippedDresses[i]);
        if (dressInfo == null) continue;

        dressInfo.status = 1;
        equippedDressesInfo.push(dressInfo);
    }

    return Responses.success(equippedDressesInfo);
}

async function useDress(userId, decorationId) {
    const dressInfo = Dressing.getDressInfo(decorationId);
    if (!dressInfo) {
        return Responses.dressNotOwned();
    }

    const vip = await Vip.fromUserId(userId);
    if (vip.getLevel() < dressInfo.getVipLevel()) {
        return Responses.dressNotOwned();
    }

    if (!dressInfo.isFree() && !vip.getLevel()) {
        const ownedDresses = await base.getOwnedDresses(userId);
        if (!ownedDresses.includes(decorationId)) {
            return Responses.dressNotOwned();
        }
    }

    if (dressInfo.clanLevel > 0) {
        const clanMember = await ClanMember.fromUserId(userId);
        if (clanMember.getClanId() == 0) {
            return Responses.notInClan();
        }

        const clan = await Clan.fromClanId(clanMember.getClanId());
        if (clan.getLevel() < dressInfo.clanLevel) {
            return Responses.inadequateClanLevel();
        }
    }

    const equippedDresses = await base.getEquippedDresses(userId);
    if (equippedDresses.includes(decorationId)) {
        return Responses.success(dressInfo);
    }

    for (let i = 0; i < equippedDresses.length; i++) {
        let typeId = decorationId.toString().substring(0, decorationId.toString().length - 5);
        if (equippedDresses[i].toString().startsWith(typeId)) {
            equippedDresses.splice(i, 1);
        }
    }

    equippedDresses.push(decorationId);
    await base.setEquippedDresses(userId, equippedDresses);

    return Responses.success(dressInfo);
}

async function removeDress(userId, decorationId) {
    const dressInfo = Dressing.getDressInfo(decorationId);
    if (!dressInfo) {
        return Responses.dressNotOwned();
    }

    const vip = await Vip.fromUserId(userId);
    if (vip.getLevel() < dressInfo.getVipLevel()) {
        return Responses.dressNotOwned();
    }
    
    if (!dressInfo.isFree() && !vip.getLevel()) {
        const ownedDresses = await base.getOwnedDresses(userId);
        if (!ownedDresses.includes(decorationId)) {
            return Responses.dressNotOwned();
        }
    }

    const equippedDresses = await base.getEquippedDresses(userId);
    if (!equippedDresses.includes(decorationId)) {
        return Responses.success();
    }

    for (let i = 0; i < equippedDresses.length; i++) {
        if (equippedDresses[i] == decorationId) {
            equippedDresses.splice(i, 1);
        }
    }

    await base.setEquippedDresses(userId, equippedDresses);
    return Responses.success();
}

module.exports = {
    getDressListByType: getDressListByType,
    getEquippedDresses: getEquippedDresses,
    useDress: useDress,
    removeDress: removeDress
}

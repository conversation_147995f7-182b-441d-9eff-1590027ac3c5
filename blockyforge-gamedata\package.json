{"name": "blockyforge-gamedata-api", "version": "1.0.0", "description": "Blocky Forge GameData APIs", "main": "src/app.js", "scripts": {"dev": "nodemon ."}, "_moduleAliases": {"@common": "../blockyforge-common/src/", "@common-config": "../blockyforge-common/src/config", "@common-constants": "../blockyforge-common/src/constants", "@common-models": "../blockyforge-common/src/models", "@config": "src/config", "@models": "src/models", "@gamedata-service": "src/service"}, "dependencies": {"fastify": "^5.3.3", "mariadb": "^3.2.3", "module-alias": "^2.2.3", "redis": "^4.6.13"}, "devDependencies": {"nodemon": "^3.1.0"}}
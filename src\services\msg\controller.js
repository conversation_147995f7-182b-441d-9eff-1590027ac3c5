const ParamValidator = require("@common/ParamValidator");
const service = require("@msg-service/service");

async function getGroupChatList(request) {
    const { pageNo, pageSize } = ParamValidator.checkPageParams(request.query.pageNo, request.query.pageSize);
    return await service.getGroupChatList(request.getUserId(), pageNo, pageSize);
}

async function getGroupChatPrice(request) {
    return await service.getGroupChatPrice(request.getUserId());
}

module.exports = [
    {
        "path": "/msg/api/v1/msg/group/chat/list",
        "methods": ["GET"],
        "functions": [getGroupChatList]
    },
    {
        "path" : "/msg/api/v1/group/chat/price",
        "methods": ["GET"],
        "functions": [getGroupChatPrice]
    }
]
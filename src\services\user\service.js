const ConfigManager = require("@common/ConfigManager");
const Responses = require("@common/Responses");
const Redis = require("@common/Redis");
const RedisKeys = require("@common/RedisKeys");
const userConfig = require("@common-config/user");
const signinConfig = require("@config/signin");
const discordConfig = require("@common-config/discord")
const Currencies = require("@common-constants/Currencies");
const DailyLoginStatuses = require("@common-constants/DailyLoginStatuses");
const Localization = require("@common-models/Localization");
const User = require("@common-models/User");
const Vip = require("@common-models/Vip");
const payService = require("@pay-service/base");
const Logger = require("@common/Logger");
const ServerTime = require("@common/ServerTime");

async function getUserInfo(userId) {
    const user = await User.fromUserId(userId);
    const vip = await Vip.fromUserId(userId);

    return Responses.success({
        ...user.response(),
        ...vip.response()
    });
}

async function setUserInfo(userId, details) {
    const user = await User.fromUserId(userId);

    if (details) {
        user.setDetails(details);
    } else {
        user.setDetails("");
    }

    await user.save();

    return Responses.success(user);
}

async function setUserIcon(userId, discordId) {
    let avatarLink;

    if (discordId) {
        const lookupUrl = `${discordConfig.LOOKUP_BASE_ENDPOINT}/${discordId}`;
        const response = await fetch(lookupUrl);
        const discordData = await response.json();
        avatarLink = discordData.avatar.link;
    }

    const user = await User.fromUserId(userId);
    if (avatarLink) {
        user.setProfilePic(avatarLink);
    } else {
        user.setProfilePic("");
    }

    await user.save();
    return Responses.success(user);
}

async function createProfile(userId, nickName, sex) {
    const isUserExists = await User.exists(userId);
    if (isUserExists) {
        return Responses.profileExists();
    }

    const user = new User(userId);
    user.setSex(sex);

    await user.changeNickname(nickName);
    await user.create();

    const vip = new Vip(userId);
    // TODO: Add country code based on IP
    // const userLocale = new Localization(userId);
    // userLocale.setCountry("");
    // await userLocale.save();

    return Responses.success({
        ...user.response(),
        ...vip.response()
    });
}

async function changeNickName(userId, newNickname) {
    const user = await User.fromUserId(userId);
    if (user.getNickname() == newNickname) {
        return Responses.invalidParameter();
    }

    let result = {};
    if (user.getIsFreeNickname()) {
        user.setIsFreeNickname(0);
    } else {
        result = await payService.removeCurrency(userId, userConfig.currencyNickname, userConfig.priceNickname);
    }

    if (result.hasFailed) {
        return Responses.notEnoughWealth();
    }

    await user.changeNickname(newNickname);
    await user.save();

    return Responses.success(user);
}

async function isChangingNameFree(userId) {
    const user = await User.fromUserId(userId);

    return Responses.success({
        currencyType: userConfig.currencyNickname,
        quantity: userConfig.priceNickname,
        free: user.getIsFreeNickname()
    });
}

async function setUserLanguage(userId, language) {
    const locale = await Localization.fromUserId(userId);

    locale.setLanguage(language);
    await locale.save();

    return Responses.success();
}

async function getUserVipInfo(userId) {
    const vip = await Vip.fromUserId(userId);
    return Responses.success(vip.response());
}

async function getDailyRewardInfo(userId) {
    const currentDay = await Redis.getKey(RedisKeys.CACHE_DAILY_LOGIN, userId) || 0;
    const hasClaimed = await Redis.getKey(RedisKeys.CACHE_DAILY_LOGIN_HAS_CLAIMED, userId);
    
    const rewards = structuredClone(signinConfig);
    const rewardList = Object.values(rewards);

    for (let i = 0; i < rewardList.length; i++) {
        if (currentDay > i) {
            rewardList[i].status = DailyLoginStatuses.CLAIMED;
        } else if (currentDay == i && !hasClaimed) {
            rewardList[i].status = DailyLoginStatuses.NOT_CLAIMED;
        } else {
            rewardList[i].status = DailyLoginStatuses.UNAVAILABLE;
        }
    }

    return Responses.success(rewards);
}

async function receiveDailyReward(userId) {
    const rewards = structuredClone(signinConfig);
    const rewardList = Object.values(rewards);

    const currentDay = await Redis.getKey(RedisKeys.CACHE_DAILY_LOGIN, userId) || 0;

    for (let i = 0; i < rewardList.length; i++) {
        const reward = rewardList[currentDay];
        switch (reward.type) {
            case "gold":
                await payService.addCurrency(userId, Currencies.GOLD, reward.quantity);
                break;
            case "diamond":
                await payService.addCurrency(userId, Currencies.DIAMOND, reward.quantity);
                break;
        }
    }

    await Redis.setKey({
        key: RedisKeys.CACHE_DAILY_LOGIN, params: [userId]
    }, currentDay + 1);

    await Redis.setKey({
        key: RedisKeys.CACHE_DAILY_LOGIN_HAS_CLAIMED, params: [userId]
    }, "1", ServerTime.getTodayTimeLeft());

    return Responses.success();
}

async function getDailyTasksAdConfig(userId) {
    Logger.warn("GetDailyTasksAdConfig: Implementation needed");
    return Responses.success();
}

async function reportUser(userId) {
    Logger.warn("ReportUser: Implementation needed");
    return Responses.innerError();
}

async function getUserInfoReward(userId) {
    const { balance } = await payService.addCurrency(userId, Currencies.DIAMOND, 5);
    return Responses.success(balance);
}

module.exports = {
    createProfile: createProfile,
    changeNickName: changeNickName,
    isChangingNameFree: isChangingNameFree,
    setUserInfo: setUserInfo,
    getUserInfo: getUserInfo,
    setUserIcon: setUserIcon,
    setUserLanguage: setUserLanguage,
    getUserVipInfo: getUserVipInfo,
    getDailyRewardInfo: getDailyRewardInfo,
    receiveDailyReward: receiveDailyReward,
    getDailyTasksAdConfig: getDailyTasksAdConfig,
    reportUser: reportUser,
    getUserInfoReward: getUserInfoReward
}

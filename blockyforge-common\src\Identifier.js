const crypto = require("crypto");
const MariaDB = require("@common/MariaDB");

module.exports = class Identifier {
    static async getNextUserId() {
        const maxUserId = await MariaDB.findFirst("SELECT MAX(userId) FROM account;", "MAX(userId)", 0);
        const nextUserId = Number(maxUserId) + 16;
        return nextUserId;
    }

    static async getNextClanId() {
        const maxClanId = await MariaDB.findFirst("SELECT MAX(clanId) FROM clan;", "MAX(clanId)", 0);
        const nextClanId = Number(maxClanId) + 4;
        return nextClanId;
    }

    static async getOrderId(userId, amount) {
        const payload = `${userId}${amount}${Date.now()}`;
        const result = crypto.hash("sha1", this.toString(payload));
        return result;
    }
}